/**
 * 用户认证相关工具函数
 * 支持无界微前端架构，优先从主项目获取用户信息
 */

const TOKEN_KEY = 'token'
const USER_INFO_KEY = 'userInfo'

/**
 * 检查是否在无界微前端环境中
 * @returns {boolean} 是否在微前端环境
 */
export const isInWujie = () => {
  return window.__WUJIE || window.$wujie
}

/**
 * 从主项目获取用户信息
 * @returns {Object|null} 主项目传递的用户信息
 */
export const getMainAppUserInfo = () => {
  if (isInWujie()) {
    try {
      // 尝试从无界通信中获取用户信息
      if (window.$wujie?.props?.userInfo) {
        return window.$wujie.props.userInfo
      }
      // 或者从全局变量获取
      if (window.__MAIN_APP_USER_INFO__) {
        return window.__MAIN_APP_USER_INFO__
      }
    } catch (error) {
      console.warn('获取主项目用户信息失败:', error)
    }
  }
  return null
}

/**
 * 从主项目获取token
 * @returns {string|null} 主项目传递的token
 */
export const getMainAppToken = () => {
  if (isInWujie()) {
    try {
      console.log('Wujie环境检测:', window.$wujie, window.__MAIN_APP_PERMISSION__);
      // 优先从全局权限对象获取
      if (window.__MAIN_APP_PERMISSION__?.token) {
        console.log('从__MAIN_APP_PERMISSION__获取token:', window.__MAIN_APP_PERMISSION__.token);
        return window.__MAIN_APP_PERMISSION__.token;
      }
      // 其次从props获取
      if (window.$wujie?.props?.token) {
        console.log('从$wujie.props获取token:', window.$wujie.props.token);
        return window.$wujie.props.token
      }
      // 或者从用户信息中获取
      const userInfo = getMainAppUserInfo()
      if (userInfo?.token) {
        return userInfo.token
      }
      // 或者从全局变量获取
      if (window.__MAIN_APP_TOKEN__) {
        return window.__MAIN_APP_TOKEN__
      }
    } catch (error) {
      console.warn('获取主项目token失败:', error)
    }
  }
  return null
}

/**
 * 获取token
 * 优先从主项目获取，其次从本地存储获取
 * @returns {string|null} token值
 */
export const getToken = () => {
  // 优先从主项目获取token
  const mainAppToken = getMainAppToken()
  if (mainAppToken) {
    return mainAppToken
  }

  // 如果主项目没有token，则从本地存储获取
  return localStorage.getItem(TOKEN_KEY) || sessionStorage.getItem(TOKEN_KEY)
}

/**
 * 设置token
 * @param {string} token token值
 * @param {boolean} remember 是否记住登录状态（使用localStorage）
 */
export const setToken = (token, remember = false) => {
  if (remember) {
    localStorage.setItem(TOKEN_KEY, token)
    // 如果选择记住，清除sessionStorage中的token
    sessionStorage.removeItem(TOKEN_KEY)
  } else {
    sessionStorage.setItem(TOKEN_KEY, token)
    // 如果不记住，清除localStorage中的token
    localStorage.removeItem(TOKEN_KEY)
  }
}

/**
 * 移除token
 */
export const removeToken = () => {
  localStorage.removeItem(TOKEN_KEY)
  sessionStorage.removeItem(TOKEN_KEY)
}

/**
 * 获取用户信息
 * 优先从主项目获取，其次从本地存储获取
 * @returns {Object|null} 用户信息对象
 */
export const getUserInfo = () => {
  // 优先从主项目获取用户信息
  const mainAppUserInfo = getMainAppUserInfo()
  if (mainAppUserInfo) {
    return mainAppUserInfo
  }

  // 如果主项目没有用户信息，则从本地存储获取
  const userInfo = localStorage.getItem(USER_INFO_KEY) || sessionStorage.getItem(USER_INFO_KEY)
  return userInfo ? JSON.parse(userInfo) : null
}

/**
 * 设置用户信息
 * @param {Object} userInfo 用户信息对象
 * @param {boolean} remember 是否记住登录状态
 */
export const setUserInfo = (userInfo, remember = false) => {
  const userInfoStr = JSON.stringify(userInfo)
  if (remember) {
    localStorage.setItem(USER_INFO_KEY, userInfoStr)
    sessionStorage.removeItem(USER_INFO_KEY)
  } else {
    sessionStorage.setItem(USER_INFO_KEY, userInfoStr)
    localStorage.removeItem(USER_INFO_KEY)
  }
}

/**
 * 移除用户信息
 */
export const removeUserInfo = () => {
  localStorage.removeItem(USER_INFO_KEY)
  sessionStorage.removeItem(USER_INFO_KEY)
}

/**
 * 清除所有认证信息
 */
export const clearAuth = () => {
  removeToken()
  removeUserInfo()
}

/**
 * 检查是否已登录
 * @returns {boolean} 是否已登录
 */
export const isLoggedIn = () => {
  return !!getToken()
}

/**
 * 获取用户权限信息
 * @returns {Array|null} 用户权限列表
 */
export const getUserPermissions = () => {
  const userInfo = getUserInfo()
  return userInfo?.permissions || userInfo?.roles || null
}

/**
 * 检查用户是否有指定权限
 * @param {string} permission 权限标识
 * @returns {boolean} 是否有权限
 */
export const hasPermission = (permission) => {
  const permissions = getUserPermissions()
  if (!permissions || !Array.isArray(permissions)) {
    return false
  }
  return permissions.includes(permission)
}

/**
 * 向主项目发送消息
 * @param {string} type 消息类型
 * @param {any} data 消息数据
 */
export const sendMessageToMainApp = (type, data) => {
  if (isInWujie()) {
    try {
      // 使用无界的通信机制
      if (window.$wujie?.bus) {
        window.$wujie.bus.$emit(type, data)
      }
      // 或者使用postMessage
      if (window.parent !== window) {
        window.parent.postMessage({
          type: 'CHILD_APP_MESSAGE',
          subType: type,
          data: data,
          from: 'data-management-module'
        }, '*')
      }
    } catch (error) {
      console.warn('向主项目发送消息失败:', error)
    }
  }
}

/**
 * 监听主项目消息
 * @param {Function} callback 消息回调函数
 */
export const listenMainAppMessage = (callback) => {
  if (isInWujie()) {
    try {
      // 监听无界通信
      if (window.$wujie?.bus) {
        window.$wujie.bus.$on('MAIN_APP_MESSAGE', callback)
      }

      // 监听postMessage
      window.addEventListener('message', (event) => {
        if (event.data?.type === 'MAIN_APP_MESSAGE') {
          callback(event.data)
        }
      })
    } catch (error) {
      console.warn('监听主项目消息失败:', error)
    }
  }
}

/**
 * 检查token是否过期（需要后端配合）
 * @param {string} token token值
 * @returns {boolean} 是否过期
 */
export const isTokenExpired = (token) => {
  if (!token) return true
  
  try {
    // 如果token是JWT格式，可以解析过期时间
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Date.now() / 1000
    return payload.exp < currentTime
  } catch (error) {
    // 如果不是JWT格式或解析失败，返回false（由后端验证）
    return false
  }
}

export default {
  getToken,
  setToken,
  removeToken,
  getUserInfo,
  setUserInfo,
  removeUserInfo,
  clearAuth,
  isLoggedIn,
  isTokenExpired
}
