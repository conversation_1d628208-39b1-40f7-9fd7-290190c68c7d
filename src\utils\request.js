import axios from 'axios';
import { getToken } from './auth';

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 获取token
    const token = getToken();
    // 如果token存在，则添加到请求头
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('已添加token到请求头:', token);
    } else {
      console.warn('未获取到token，无法添加到请求头');
    }
    return config;
  },
  (error) => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('响应拦截器错误:', error);
    // 处理401未授权错误
    if (error.response && error.response.status === 401) {
      console.error('token无效或已过期，需要重新获取');
      // 可以在这里添加重新获取token或跳转登录的逻辑
    }
    return Promise.reject(error);
  }
);

export default service;