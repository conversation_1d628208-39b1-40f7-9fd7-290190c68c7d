import { getDataSourceList } from '@/api/datainfor/dataupload.js'
import { getDataSetList } from '@/api/datainfor/dataset.js'

/**
 * 测试API连通性
 */
export const testApiConnection = async () => {
  console.log('开始测试API连通性...')
  console.log('前端请求地址:', import.meta.env.VITE_API_BASE_URL || '/api')
  console.log('实际后端地址:', import.meta.env.VITE_BACKEND_URL || 'http://237850r72u.zicp.vip/')
  console.log('当前环境:', import.meta.env.MODE)

  const results = {
    dataSourceList: { success: false, error: null, response: null },
    dataSetList: { success: false, error: null, response: null }
  }

  // 测试数据源列表接口
  try {
    console.log('测试数据源列表接口: GET /api/datasource/list (代理到后端 /datasource/list)')
    const dataSourceResponse = await getDataSourceList()
    console.log('数据源列表接口响应:', dataSourceResponse)
    results.dataSourceList.success = true
    results.dataSourceList.response = dataSourceResponse
  } catch (error) {
    console.error('数据源列表接口测试失败:', error)
    results.dataSourceList.error = error.message || error.toString()

    // 添加更详细的错误信息
    if (error.response) {
      results.dataSourceList.error += ` (状态码: ${error.response.status})`
    } else if (error.code) {
      results.dataSourceList.error += ` (错误代码: ${error.code})`
    }
  }

  // 测试数据集列表接口
  try {
    console.log('测试数据集列表接口: GET /api/dataset/list (代理到后端 /dataset/list)')
    const dataSetResponse = await getDataSetList({ pageNo: 1, pageSize: 10 })
    console.log('数据集列表接口响应:', dataSetResponse)
    results.dataSetList.success = true
    results.dataSetList.response = dataSetResponse
  } catch (error) {
    console.error('数据集列表接口测试失败:', error)
    results.dataSetList.error = error.message || error.toString()

    // 添加更详细的错误信息
    if (error.response) {
      results.dataSetList.error += ` (状态码: ${error.response.status})`
    } else if (error.code) {
      results.dataSetList.error += ` (错误代码: ${error.code})`
    }
  }

  console.log('API连通性测试结果:', results)
  return results
}
