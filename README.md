# 智能数据分析平台 - 数据管理模块

基于Vue3 + Element Plus构建的数据管理平台，包含数据源管理和数据集管理功能。

## 功能特性

- 🗄️ **数据源管理**: 支持数据库、本地文件、API等多种数据源类型
- 📊 **数据集管理**: 数据集的创建、编辑、删除和权限管理
- 🔍 **搜索筛选**: 支持多维度搜索和高级筛选
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🎨 **现代化UI**: 基于Element Plus的美观界面

## 技术栈

- Vue 3.4+
- Vue Router 4.2+
- Element Plus 2.4+
- Vite 5.0+
- Sass

## 项目结构

```
src/
├── api/                 # API接口
│   └── datainfor/      # 数据相关API
├── components/         # 通用组件
│   └── GeneralTables/  # 通用表格组件
├── router/             # 路由配置
├── styles/             # 全局样式
└── views/              # 页面组件
    ├── dataSource/     # 数据源页面
    └── dataSet/        # 数据集页面
```

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 路由说明

- `/datasource` - 数据源管理页面（默认页面）
- `/dataset` - 数据集管理页面

## 主要功能

### 数据源管理
- 左侧数据源列表，支持数据库、本地文件、API三种类型
- 右侧数据源详情，显示数据表信息
- 支持数据源的新建、编辑、删除操作
- 支持数据预览和字段详情查看

### 数据集管理
- 数据集列表展示，支持分页
- 高级筛选功能（创建者、权限、来源）
- 支持按创建时间或修改时间排序
- 数据集的权限分配和问数配置

## 开发说明

项目使用模拟API数据，实际使用时需要替换为真实的后端接口。

API文件位置：
- `src/api/datainfor/dataupload.js` - 数据源相关API
- `src/api/datainfor/dataset.js` - 数据集相关API
