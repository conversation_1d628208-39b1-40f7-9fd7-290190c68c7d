import axios from 'axios'
import { ElMessage, ElLoading } from 'element-plus'
import config from './index.js'
import { getToken, clearAuth } from '@/utils/auth.js'
import { requestTokenRefresh, notifyError, isWujieEnvironment } from '@/utils/wujie.js'

// 创建axios实例
const service = axios.create({
  baseURL: config.baseURL,
  timeout: config.timeout,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  },
  // 允许跨域请求携带凭证
  withCredentials: false
})

// 请求加载实例
let loadingInstance = null

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 关闭之前的loading实例
    if (loadingInstance) {
      loadingInstance.close()
    }

    // 显示加载动画
    loadingInstance = ElLoading.service({
      lock: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 添加用户token认证
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
      console.log('已添加token到请求头:', token)
    } else if (isWujieEnvironment()) {
      // 在无界环境中但没有token时，记录警告
      console.warn('在无界环境中但未获取到token，请检查主项目是否正确传递了用户认证信息')
    }

    console.log('请求发送:', config.method?.toUpperCase(), config.url, config.data || config.params)
    return config
  },
  error => {
    // 关闭加载动画
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
    console.error('请求错误:', error)
    ElMessage.error('请求发送失败')
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 关闭加载动画
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }

    console.log('响应接收:', response.config.url, response.data)

    const { data, status } = response

    // 根据后端返回的数据结构进行调整
    if (status === 200) {
      // 如果后端返回的数据有特定的成功标识，可以在这里判断
      // 例如: if (data.code === 200) return data.data
      // 这里假设后端直接返回数据，如果有包装结构请根据实际情况调整
      return data
    } else {
      const message = (data && data.message) || '请求失败'
      ElMessage.error(message)
      return Promise.reject(new Error(message))
    }
  },
  error => {
    // 关闭加载动画
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }

    console.error('响应错误:', error)

    let message = '网络错误'

    if (error.response) {
      const { status, data } = error.response
      switch (status) {
        case 400:
          message = (data && data.message) || '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          // 处理登录过期逻辑
          if (isWujieEnvironment()) {
            // 在无界环境中，通知主项目token过期
            requestTokenRefresh()
            message = 'Token已过期，正在请求主项目刷新...'
          } else {
            // 独立运行时，清除本地认证信息
            clearAuth()
            // 可以在这里跳转到登录页面
            // window.location.href = '/login'
          }
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = (data && data.message) || `连接错误${status}`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.message && error.message.includes('Network Error')) {
      message = '网络连接异常，请检查网络或服务器状态'
    } else if (error.message) {
      message = error.message
    }

    ElMessage.error(message)

    // 如果在无界环境中，通知主项目发生了错误
    if (isWujieEnvironment()) {
      notifyError(error, `HTTP ${error.response?.status || 'Network'} Error`)
    }

    return Promise.reject(error)
  }
)

export default service
