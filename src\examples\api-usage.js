/**
 * API使用示例
 * 展示如何正确使用修改后的API接口
 */

import { 
  getDataSourceList,
  getDataSourceWithTables,
  getFieldDetail,
  getDataDetail,
  updateDataSource,
  deleteDataSource,
  getDatabaseConfig
} from '@/api/datainfor/dataupload.js'

import { setToken, getToken, clearAuth, isLoggedIn } from '@/utils/auth.js'

/**
 * 示例1: 用户登录后设置token
 */
export const loginExample = () => {
  // 假设登录成功后获得token
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  
  // 设置token（记住登录状态）
  setToken(token, true)
  
  console.log('Token已设置，当前登录状态:', isLoggedIn())
}

/**
 * 示例2: 获取数据源列表
 */
export const fetchDataSourcesExample = async () => {
  try {
    // 检查是否已登录
    if (!isLoggedIn()) {
      console.error('请先登录')
      return
    }

    // 获取所有数据源
    const allDataSources = await getDataSourceList()
    console.log('所有数据源:', allDataSources)

    // 按名称搜索数据源
    const searchResult = await getDataSourceList({ 
      dataSourceName: '测试数据源' 
    })
    console.log('搜索结果:', searchResult)

  } catch (error) {
    console.error('获取数据源列表失败:', error)
  }
}

/**
 * 示例3: 获取数据源详情和表列表
 */
export const fetchDataSourceDetailsExample = async (dataSourceId) => {
  try {
    const params = {
      pageNo: 1,
      pageSize: 10,
      tableName: '' // 可选，用于搜索特定表名
    }

    const result = await getDataSourceWithTables(dataSourceId, params)
    console.log('数据源详情和表列表:', result)

  } catch (error) {
    console.error('获取数据源详情失败:', error)
  }
}

/**
 * 示例4: 获取字段详情
 */
export const fetchFieldDetailsExample = async () => {
  try {
    const params = {
      dataBaseId: 'db123',
      tableName: 'users',
      sheetId: 'sheet1' // Excel文件时需要
    }

    const fields = await getFieldDetail(params)
    console.log('字段详情:', fields)

  } catch (error) {
    console.error('获取字段详情失败:', error)
  }
}

/**
 * 示例5: 获取数据预览
 */
export const fetchDataPreviewExample = async () => {
  try {
    const params = {
      dataBaseId: 'db123',
      tableName: 'users',
      pageNo: 1,
      pageSize: 20,
      sheetId: 'sheet1' // Excel文件时需要
    }

    const preview = await getDataDetail(params)
    console.log('数据预览:', preview)

  } catch (error) {
    console.error('获取数据预览失败:', error)
  }
}

/**
 * 示例6: 更新数据源
 */
export const updateDataSourceExample = async () => {
  try {
    const dataSourceInfo = {
      id: 'ds123',
      name: '更新后的数据源名称',
      description: '数据源描述',
      // 其他数据源属性...
    }

    const result = await updateDataSource(dataSourceInfo)
    console.log('更新结果:', result)

  } catch (error) {
    console.error('更新数据源失败:', error)
  }
}

/**
 * 示例7: 删除数据源
 */
export const deleteDataSourceExample = async (dataSourceId) => {
  try {
    // 确认删除
    if (confirm('确定要删除这个数据源吗？')) {
      const result = await deleteDataSource(dataSourceId)
      console.log('删除结果:', result)
    }

  } catch (error) {
    console.error('删除数据源失败:', error)
  }
}

/**
 * 示例8: 获取数据库配置
 */
export const fetchDatabaseConfigExample = async (typeId) => {
  try {
    const config = await getDatabaseConfig(typeId)
    console.log('数据库配置:', config)

  } catch (error) {
    console.error('获取数据库配置失败:', error)
  }
}

/**
 * 示例9: 错误处理和重新登录
 */
export const errorHandlingExample = async () => {
  try {
    // 尝试获取数据
    const data = await getDataSourceList()
    console.log('数据获取成功:', data)

  } catch (error) {
    if (error.response && error.response.status === 401) {
      // Token过期或无效，已自动清除认证信息
      console.log('需要重新登录')
      // 跳转到登录页面
      // router.push('/login')
    } else {
      console.error('其他错误:', error)
    }
  }
}

/**
 * 示例10: 完整的工作流程
 */
export const completeWorkflowExample = async () => {
  try {
    // 1. 检查登录状态
    if (!isLoggedIn()) {
      console.log('用户未登录，需要先登录')
      return
    }

    // 2. 获取数据源列表
    const dataSources = await getDataSourceList()
    console.log('步骤1 - 获取数据源列表:', dataSources)

    if (dataSources && dataSources.length > 0) {
      const firstDataSource = dataSources[0]
      
      // 3. 获取第一个数据源的表列表
      const tables = await getDataSourceWithTables(firstDataSource.id, {
        pageNo: 1,
        pageSize: 10
      })
      console.log('步骤2 - 获取表列表:', tables)

      if (tables && tables.data && tables.data.length > 0) {
        const firstTable = tables.data[0]
        
        // 4. 获取第一个表的字段详情
        const fields = await getFieldDetail({
          dataBaseId: firstDataSource.id,
          tableName: firstTable.tableName
        })
        console.log('步骤3 - 获取字段详情:', fields)

        // 5. 获取数据预览
        const preview = await getDataDetail({
          dataBaseId: firstDataSource.id,
          tableName: firstTable.tableName,
          pageNo: 1,
          pageSize: 5
        })
        console.log('步骤4 - 获取数据预览:', preview)
      }
    }

  } catch (error) {
    console.error('工作流程执行失败:', error)
  }
}

/**
 * 示例11: 登出
 */
export const logoutExample = () => {
  // 清除所有认证信息
  clearAuth()
  console.log('已登出，当前登录状态:', isLoggedIn())
  
  // 跳转到登录页面
  // router.push('/login')
}

// 导出所有示例函数
export default {
  loginExample,
  fetchDataSourcesExample,
  fetchDataSourceDetailsExample,
  fetchFieldDetailsExample,
  fetchDataPreviewExample,
  updateDataSourceExample,
  deleteDataSourceExample,
  fetchDatabaseConfigExample,
  errorHandlingExample,
  completeWorkflowExample,
  logoutExample
}
