# 无界微前端集成指南

## 概述

本项目已配置为支持无界(wujie)微前端架构，可以作为子应用集成到主项目中。主项目可以通过props传递用户信息、token、权限等数据，子应用会自动使用这些信息进行API请求认证。

## 主要特性

- ✅ **自动Token认证**: 优先使用主项目传递的token
- ✅ **用户信息共享**: 自动获取主项目的用户信息和权限
- ✅ **双向通信**: 支持子应用向主项目发送事件
- ✅ **错误处理**: 自动向主项目报告错误和token过期
- ✅ **路由同步**: 自动通知主项目路由变化
- ✅ **独立运行**: 支持独立开发和运行

## 架构说明

### 数据流向
```
主项目 → 子应用: 用户信息、token、权限、配置
子应用 → 主项目: 事件通知、错误报告、路由变化
```

### 通信机制
1. **Props传递**: 主项目通过props传递初始数据
2. **事件总线**: 使用无界的事件总线进行双向通信
3. **PostMessage**: 备用通信方式
4. **全局变量**: 紧急情况下的数据传递

## 主项目集成

### 1. 安装无界
```bash
npm install wujie-vue3
```

### 2. 注册无界
```javascript
// main.js
import { createApp } from 'vue'
import WujieVue from 'wujie-vue3'
import App from './App.vue'

const app = createApp(App)
app.use(WujieVue)
app.mount('#app')
```

### 3. 使用子应用
```vue
<template>
  <WujieVue
    width="100%"
    height="100%"
    name="data-management"
    :url="childAppUrl"
    :props="childAppProps"
    @error="handleError"
  />
</template>

<script setup>
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const childAppUrl = 'http://localhost:3000'

const childAppProps = computed(() => ({
  userInfo: userStore.userInfo,
  token: userStore.token,
  tenantId: userStore.tenantId,
  config: {
    apiBaseUrl: 'http://237850r72u.zicp.vip/basic'
  }
}))
</script>
```

## Props数据格式

### 必需的Props
```javascript
{
  // 用户认证token
  token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  
  // 用户信息
  userInfo: {
    id: "123456",
    name: "张三",
    email: "<EMAIL>",
    avatar: "/avatars/zhangsan.jpg",
    permissions: ["data:read", "data:write", "data:delete"],
    roles: ["admin", "data_manager"]
  }
}
```

### 可选的Props
```javascript
{
  // 租户ID
  tenantId: "tenant_001",
  
  // 配置信息
  config: {
    apiBaseUrl: "http://237850r72u.zicp.vip/basic",
    features: {
      dataExport: true,
      dataImport: true,
      advancedFilter: true
    }
  }
}
```

## 事件通信

### 子应用发送的事件

| 事件名 | 说明 | 数据格式 |
|--------|------|----------|
| `CHILD_APP_READY` | 子应用准备就绪 | `{ appName, version, timestamp }` |
| `ROUTE_CHANGE` | 路由变化 | `{ path, name, params, query }` |
| `REQUEST_TOKEN_REFRESH` | 请求刷新token | `{ reason, timestamp }` |
| `CHILD_APP_ERROR` | 子应用错误 | `{ message, stack, context, timestamp }` |
| `DATA_CHANGE` | 数据变化通知 | `{ type, data, timestamp }` |

### 主项目发送的事件

| 事件名 | 说明 | 数据格式 |
|--------|------|----------|
| `USER_INFO_UPDATE` | 用户信息更新 | `{ id, name, email, ... }` |
| `TOKEN_UPDATE` | Token更新 | `"new_token_string"` |
| `PERMISSIONS_UPDATE` | 权限更新 | `["permission1", "permission2"]` |

## 在子应用中使用

### 获取用户信息
```javascript
import { getUserInfo, getToken, hasPermission } from '@/utils/auth.js'

// 获取用户信息（优先从主项目获取）
const userInfo = getUserInfo()

// 获取token（优先从主项目获取）
const token = getToken()

// 检查权限
const canDelete = hasPermission('data:delete')
```

### 发送事件到主项目
```javascript
import { emitToMainApp } from '@/utils/wujie.js'

// 通知数据变化
emitToMainApp('DATA_CHANGE', {
  type: 'datasource_updated',
  data: { id: 'ds_123', name: '新数据源' },
  timestamp: Date.now()
})
```

### 监听主项目事件
```javascript
import { listenMainAppEvent } from '@/utils/wujie.js'

// 监听用户信息更新
const unsubscribe = listenMainAppEvent('USER_INFO_UPDATE', (userInfo) => {
  console.log('用户信息已更新:', userInfo)
})

// 组件销毁时取消监听
onUnmounted(() => {
  unsubscribe?.()
})
```

## 开发模式

### 独立开发
```bash
npm run dev
```
在独立模式下，项目会使用本地存储的认证信息。

### 微前端模式
在主项目中集成时，会自动检测无界环境并使用主项目传递的数据。

## 部署配置

### 子应用部署
```bash
npm run build
```

### 主项目配置
```javascript
// 生产环境URL
const childAppUrl = 'https://your-domain.com/data-management'

// 开发环境URL
const childAppUrl = 'http://localhost:3000'
```

## 错误处理

### Token过期
当API返回401错误时：
1. 在微前端模式下，自动向主项目请求刷新token
2. 在独立模式下，清除本地认证信息

### 网络错误
所有网络错误都会：
1. 显示用户友好的错误提示
2. 在微前端模式下通知主项目

## 调试技巧

### 检查环境
```javascript
import { isWujieEnvironment } from '@/utils/wujie.js'

if (isWujieEnvironment()) {
  console.log('运行在微前端模式')
} else {
  console.log('运行在独立模式')
}
```

### 查看传递的数据
```javascript
import { getMainAppProps } from '@/utils/wujie.js'

console.log('主项目传递的props:', getMainAppProps())
```

### 监听所有事件
```javascript
// 在开发环境中监听所有事件
if (process.env.NODE_ENV === 'development') {
  window.addEventListener('message', (event) => {
    console.log('收到消息:', event.data)
  })
}
```

## 注意事项

1. **Token优先级**: 主项目token > 本地存储token
2. **用户信息优先级**: 主项目用户信息 > 本地存储用户信息
3. **路由模式**: 建议使用hash模式避免路由冲突
4. **样式隔离**: 无界会自动处理样式隔离
5. **全局变量**: 避免污染主项目的全局变量

## 常见问题

### Q: 如何确保token实时更新？
A: 主项目在token更新时发送`TOKEN_UPDATE`事件，子应用会自动更新。

### Q: 如何处理权限变化？
A: 主项目发送`PERMISSIONS_UPDATE`事件，子应用重新检查权限。

### Q: 如何调试通信问题？
A: 在浏览器控制台查看相关日志，检查props传递和事件发送。

### Q: 独立运行时如何设置用户信息？
A: 使用`setToken()`和`setUserInfo()`函数设置本地认证信息。
