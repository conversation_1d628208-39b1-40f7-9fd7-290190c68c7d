# 无界微前端改造总结

## 改造概述

已成功将项目改造为支持无界(wujie)微前端架构的子应用，主要实现了：

1. **智能Token获取**: 优先从主项目获取token，支持自动认证
2. **用户信息共享**: 自动获取主项目的用户信息和权限
3. **双向通信**: 完整的主子应用通信机制
4. **兼容性**: 保持独立运行能力

## 主要修改文件

### 1. 认证工具增强 (`src/utils/auth.js`)

#### 新增功能
- `isInWujie()`: 检测是否在无界环境中
- `getMainAppUserInfo()`: 从主项目获取用户信息
- `getMainAppToken()`: 从主项目获取token
- `getUserPermissions()`: 获取用户权限
- `hasPermission()`: 权限检查
- `sendMessageToMainApp()`: 向主项目发送消息
- `listenMainAppMessage()`: 监听主项目消息

#### 优化逻辑
```javascript
// Token获取优先级：主项目 > 本地存储
export const getToken = () => {
  const mainAppToken = getMainAppToken()
  if (mainAppToken) return mainAppToken
  return localStorage.getItem(TOKEN_KEY) || sessionStorage.getItem(TOKEN_KEY)
}

// 用户信息获取优先级：主项目 > 本地存储
export const getUserInfo = () => {
  const mainAppUserInfo = getMainAppUserInfo()
  if (mainAppUserInfo) return mainAppUserInfo
  // 从本地存储获取...
}
```

### 2. 无界通信工具 (`src/utils/wujie.js`)

#### 核心功能
- **环境检测**: `isWujieEnvironment()`
- **Props获取**: `getMainAppProps()`, `getUserInfoFromProps()`, `getTokenFromProps()`
- **事件通信**: `emitToMainApp()`, `listenMainAppEvent()`
- **生命周期**: `notifyMainAppReady()`, `initWujieComm()`

#### 通信机制
```javascript
// 向主项目发送事件
export const emitToMainApp = (eventName, data) => {
  // 方式1: 无界事件总线
  if (wujie?.bus) {
    wujie.bus.$emit(eventName, data)
  }
  
  // 方式2: PostMessage
  window.parent.postMessage({
    type: 'MICRO_APP_EVENT',
    eventName,
    data
  }, '*')
}
```

### 3. 应用启动优化 (`src/main.js`)

#### 无界集成
```javascript
// 检查是否在无界环境中
if (window.__WUJIE_MOUNT__) {
  // 在无界环境中，等待挂载信号
  window.__WUJIE_MOUNT__ = mountApp
} else {
  // 独立运行，直接挂载
  mountApp()
}

// 挂载后初始化通信
const mountApp = () => {
  app.mount('#app')
  if (isWujieEnvironment()) {
    initWujieComm()
  }
}
```

### 4. 请求拦截器增强 (`src/config/request.js`)

#### Token处理
```javascript
// 优先使用主项目token
const token = getToken()
if (token) {
  config.headers.Authorization = `Bearer ${token}`
} else if (isWujieEnvironment()) {
  console.warn('在无界环境中但未获取到token')
}
```

#### 错误处理
```javascript
case 401:
  if (isWujieEnvironment()) {
    // 通知主项目token过期
    requestTokenRefresh()
  } else {
    // 独立运行时清除本地认证
    clearAuth()
  }
  break
```

### 5. 路由通知 (`src/router/index.js`)

#### 路由变化通知
```javascript
// 路由后置守卫 - 通知主项目路由变化
router.afterEach((to, from) => {
  if (isWujieEnvironment()) {
    notifyRouteChange(to)
  }
})
```

## 支持的通信事件

### 子应用 → 主项目

| 事件名 | 触发时机 | 数据格式 |
|--------|----------|----------|
| `CHILD_APP_READY` | 应用启动完成 | `{ appName, version, timestamp }` |
| `ROUTE_CHANGE` | 路由变化 | `{ path, name, params, query }` |
| `REQUEST_TOKEN_REFRESH` | Token过期 | `{ reason, timestamp }` |
| `CHILD_APP_ERROR` | 发生错误 | `{ message, stack, context }` |
| `DATA_CHANGE` | 数据变化 | `{ type, data, timestamp }` |

### 主项目 → 子应用

| 事件名 | 用途 | 数据格式 |
|--------|------|----------|
| `USER_INFO_UPDATE` | 用户信息更新 | `{ id, name, email, ... }` |
| `TOKEN_UPDATE` | Token刷新 | `"new_token_string"` |
| `PERMISSIONS_UPDATE` | 权限变化 | `["permission1", "permission2"]` |

## 主项目集成方式

### 1. Props传递
```javascript
const childAppProps = computed(() => ({
  // 必需
  token: userStore.token,
  userInfo: userStore.userInfo,
  
  // 可选
  tenantId: userStore.tenantId,
  config: {
    apiBaseUrl: 'http://237850r72u.zicp.vip/basic',
    features: { dataExport: true }
  }
}))
```

### 2. 事件监听
```javascript
const afterMount = (appWindow) => {
  // 监听子应用事件
  appWindow.$wujie?.bus?.$on('REQUEST_TOKEN_REFRESH', () => {
    refreshTokenAndNotify(appWindow)
  })
  
  appWindow.$wujie?.bus?.$on('CHILD_APP_ERROR', (error) => {
    console.error('子应用错误:', error)
  })
}
```

## 使用示例

### 在组件中获取主项目数据
```javascript
import { getUserInfo, getToken, hasPermission } from '@/utils/auth.js'
import { emitToMainApp } from '@/utils/wujie.js'

export default {
  setup() {
    // 获取用户信息（自动从主项目获取）
    const userInfo = getUserInfo()
    const token = getToken()
    const canDelete = hasPermission('data:delete')
    
    // 通知主项目数据变化
    const notifyDataChange = (data) => {
      emitToMainApp('DATA_CHANGE', {
        type: 'datasource_updated',
        data
      })
    }
    
    return {
      userInfo,
      token,
      canDelete,
      notifyDataChange
    }
  }
}
```

## 新增文件

1. **`src/utils/wujie.js`** - 无界通信工具
2. **`src/examples/wujie-usage.js`** - 使用示例
3. **`WUJIE_INTEGRATION.md`** - 集成指南
4. **`WUJIE_MODIFICATION_SUMMARY.md`** - 改造总结

## 兼容性说明

### 独立运行
- 保持原有的本地认证机制
- 所有功能正常工作
- 开发调试不受影响

### 微前端模式
- 自动检测无界环境
- 优先使用主项目数据
- 完整的通信机制

## 部署建议

### 开发环境
```bash
# 独立开发
npm run dev

# 主项目中集成
# 在主项目中配置子应用URL为 http://localhost:3000
```

### 生产环境
```bash
# 构建子应用
npm run build

# 部署到CDN或静态服务器
# 在主项目中配置子应用URL为生产地址
```

## 优势总结

1. **无缝集成**: 主项目只需传递props，无需复杂配置
2. **智能认证**: 自动使用主项目的认证信息
3. **双向通信**: 完整的事件通信机制
4. **错误处理**: 自动处理token过期和错误上报
5. **开发友好**: 保持独立开发能力
6. **生产就绪**: 完整的生产环境支持

## 注意事项

1. **Token优先级**: 主项目token优先于本地存储
2. **事件命名**: 使用统一的事件命名规范
3. **错误处理**: 所有错误都会通知主项目
4. **路由同步**: 路由变化会自动通知主项目
5. **权限检查**: 支持细粒度的权限控制

现在您的项目已完全支持无界微前端架构，可以无缝集成到主项目中，同时保持独立运行的能力！
