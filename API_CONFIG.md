# API配置说明

## 基础配置

### 后端接口地址
- **Base URL**: `http://237850r72u.zicp.vip/basic`
- **超时时间**: 10秒
- **认证方式**: Bearer Token

### 环境配置文件
- 可通过 `VITE_API_BASE_URL` 环境变量覆盖默认配置

## 配置文件结构

```
src/config/
├── index.js          # 环境配置
├── request.js        # axios实例配置
└── api.js           # API工具函数

src/utils/
└── auth.js          # 认证工具函数
```

## 认证机制

### Token管理
- **存储方式**: localStorage（记住登录）或 sessionStorage（临时登录）
- **请求头**: 自动添加 `Authorization: Bearer {token}`
- **过期处理**: 401错误时自动清除认证信息

## API接口列表

### 数据源相关接口

| 接口名称 | 方法 | 路径 | 完整URL |
|---------|------|------|---------|
| 获取数据源列表 | GET | `/datasource/list` | `http://237850r72u.zicp.vip/basic/datasource/list` |
| 获取数据源详情 | GET | `/datasource/{id}/tables` | `http://237850r72u.zicp.vip/basic/datasource/{id}/tables` |
| 获取字段详情 | GET | `/datasource/field/detail` | `http://237850r72u.zicp.vip/basic/datasource/field/detail` |
| 获取数据预览 | GET | `/datasource/data/preview` | `http://237850r72u.zicp.vip/basic/datasource/data/preview` |
| 更新数据源 | PUT | `/datasource/{id}` | `http://237850r72u.zicp.vip/basic/datasource/{id}` |
| 删除数据源 | DELETE | `/datasource/{id}` | `http://237850r72u.zicp.vip/basic/datasource/{id}` |
| 获取数据库配置 | GET | `/datasource/database/config/{typeId}` | `http://237850r72u.zicp.vip/basic/datasource/database/config/{typeId}` |

### 数据集相关接口

| 接口名称 | 方法 | 路径 | 说明 |
|---------|------|------|------|
| 获取数据集列表 | GET | `/api/dataset/list` | 获取所有数据集 |
| 删除数据集 | DELETE | `/api/dataset/{id}` | 删除指定数据集 |

## 请求参数说明

### 数据源列表查询参数
```javascript
{
  dataSourceName: string, // 数据源名称（可选）
}
```

### 数据源表列表查询参数
```javascript
{
  pageNo: number,        // 页码
  pageSize: number,      // 每页大小
  tableName: string      // 表名搜索（可选）
}
```

### 字段详情查询参数
```javascript
{
  dataBaseId: string,    // 数据库ID
  tableName: string,     // 表名
  sheetId: string        // 表格ID（Excel文件，可选）
}
```

### 数据预览查询参数
```javascript
{
  dataBaseId: string,    // 数据库ID
  tableName: string,     // 表名
  sheetId: string,       // 表格ID（Excel文件，可选）
  pageNo: number,        // 页码
  pageSize: number       // 每页大小
}
```

### 数据集列表查询参数
```javascript
{
  datasetName: string,   // 数据集名称（可选）
  pageNo: number,        // 页码
  pageSize: number,      // 每页大小
  sortField: string      // 排序字段
}
```

## 错误处理

项目已配置统一的错误处理机制：

1. **请求拦截器**: 自动添加loading动画和认证信息
2. **响应拦截器**: 统一处理错误状态码和错误消息
3. **错误提示**: 使用Element Plus的Message组件显示错误信息

## 测试功能

在数据源页面添加了"测试接口"按钮，可以验证与后端接口的连通性。

## 使用方法

1. 确保后端服务运行在 `http://237850r72u.zicp.vip/`
2. 启动前端项目: `npm run dev`
3. 访问 `http://localhost:3001/`
4. 点击"测试接口"按钮验证连通性

## 注意事项

1. 所有API调用都会显示loading动画
2. 请求失败会自动显示错误提示
3. 可以通过浏览器开发者工具查看详细的请求和响应信息
4. 如需修改接口地址，请更新 `.env.development` 和 `.env.production` 文件
