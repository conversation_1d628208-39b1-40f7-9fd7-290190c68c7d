<template>
  <el-drawer v-model="drawerShow" title="权限配置" direction="rtl" size="50%">
    <el-tabs v-model="permissionTab">
      <el-tab-pane label="普通授权" name="cooperate">
        <el-tabs v-model="cooperateTab" style="margin-bottom: 16px;">
          <el-tab-pane label="用户" name="user">
            <el-input v-model="userSearch" placeholder="搜索用户" prefix-icon="el-icon-search" style="width: 300px; margin-bottom: 16px;" />
            <el-table :data="filteredUserList" border style="width: 100%;">
              <el-table-column prop="name" label="用户" min-width="100" />
              <el-table-column label="编辑" min-width="80">
                <template #default="scope">
                  <el-radio v-model="scope.row.permission" value="edit" />
                </template>
              </el-table-column>
              <el-table-column label="使用" min-width="80">
                <template #default="scope">
                  <el-radio v-model="scope.row.permission" value="use" />
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="用户组" name="group">
            <el-input v-model="groupSearch" placeholder="搜索用户组" prefix-icon="el-icon-search" style="width: 300px; margin-bottom: 16px;" />
            <el-table :data="filteredGroupList" border style="width: 100%;">
              <el-table-column prop="name" label="用户组" min-width="100" />
              <el-table-column label="编辑" min-width="80">
                <template #default="scope">
                  <el-radio v-model="scope.row.permission" value="edit" />
                </template>
              </el-table-column>
              <el-table-column label="使用" min-width="80">
                <template #default="scope">
                  <el-radio v-model="scope.row.permission" value="use" />
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
      <el-tab-pane label="行级权限" name="row">
        <div class="row-permission-panel">
          <template v-if="!rowEditMode">
            <div class="row-permission-header" style="display: flex; align-items: center; justify-content: space-between;">
              <div>
                <el-switch v-model="rowLevelEnabled" class="row-switch" />
                <span class="row-switch-label">开启行级权限</span>
              </div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <span>其他用户拥有:</span>
                <el-radio-group v-model="rowUserPermission" size="small">
                  <el-radio-button value="所有权限" />
                  <el-radio-button value="无任何权限" />
                </el-radio-group>
              </div>
            </div>
            <div class="row-permission-table-panel">
              <el-button type="primary" size="small" class="add-rule-btn" @click="addRowRule">添加规则</el-button>
              <el-table :data="rowPermissionData" border class="row-permission-table" style="margin-top: 12px;">
                <el-table-column prop="name" label="规则名称" min-width="120" />
                <el-table-column prop="user" label="受限用户" min-width="160" />
                <el-table-column prop="fields" label="限定字段" min-width="180" />
                <el-table-column label="操作" min-width="100">
                  <template #default="scope">
                    <el-link type="primary" @click="editRowRule(scope.row, scope.$index)">编辑</el-link>
                    <el-link type="primary" style="margin-left: 8px;" @click="deleteRowRule(scope.row, scope.$index)">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
          <template v-else>
            <div style="background:#fff;padding:18px;border-radius:8px;">
              <div style="font-weight:bold;font-size:18px;margin-bottom:12px;">规则名称</div>
              <el-input v-model="rowEditForm.name" placeholder="请输入规则名称" style="margin-bottom:16px;width:300px;" />
              <div style="display:flex;gap:24px;">
                <div style="width:260px;border-right:1px solid #eee;padding-right:18px;">
                  <div class="edit-rule-label">编辑规则</div>
                  <el-radio-group v-model="rowEditForm.userType" class="user-type-group">
                    <el-radio value="所有用户">所有用户</el-radio>
                    <el-radio value="指定用户">指定用户</el-radio>
                  </el-radio-group>
                  <div v-if="rowEditForm.userType === '指定用户'">
                    <div style="margin-bottom:8px;">
                      <el-tag v-for="(user, idx) in rowEditForm.selectedUsers" :key="user" closable @close="removeEditRowUser(idx)" style="margin-right:4px;">{{ user }}</el-tag>
                    </div>
                    <el-select v-model="rowEditForm.userSelect" placeholder="选择用户" filterable style="width:100%;margin-bottom:8px;">
                      <el-option v-for="item in rowUserOptions" :key="item" :label="item" :value="item" />
                    </el-select>
                    <el-checkbox-group v-model="rowEditForm.userChecked">
                      <el-checkbox v-for="item in rowUserOptions" :key="item" :label="item">{{ item }}</el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
                <div style="flex:1;padding-left:18px;">
                  <div class="edit-rule-label">可见内容</div>
                  <div v-for="(cond, idx) in rowEditForm.conditions" :key="idx" style="display:flex;align-items:center;gap:8px;margin-bottom:12px;">
                    <el-select v-model="cond.field" placeholder="筛选字段" style="width:140px;">
                      <el-option v-for="f in rowFieldOptions" :key="f" :label="f" :value="f" />
                    </el-select>
                    <el-select v-model="cond.filterType" placeholder="筛选方式" style="width:120px;">
                      <el-option label="校举筛选" value="校举筛选" />
                      <el-option label="条件筛选" value="条件筛选" />
                    </el-select>
                    <el-select v-model="cond.fixedType" placeholder="固定值" style="width:100px;">
                      <el-option label="固定值" value="固定值" />
                    </el-select>
                    <el-input v-model="cond.fixedValue" placeholder="输入值" style="width:180px;" />
                    <el-button v-if="rowEditForm.conditions.length > 1" icon="el-icon-delete" @click="removeEditCondition(idx)" circle size="small" />
                  </div>
                  <el-button size="small" @click="addEditCondition">添加条件</el-button>
                </div>
              </div>
              <div style="text-align:right;margin-top:24px;">
                <el-button size="small" @click="cancelRowEdit">取消</el-button>
                <el-button size="small" type="primary" @click="saveRowRule">保存</el-button>
              </div>
            </div>
          </template>
        </div>
      </el-tab-pane>
      <el-tab-pane label="列级权限" name="col">
        <div class="row-permission-panel">
          <template v-if="!colEditMode">
            <div class="row-permission-header" style="display: flex; align-items: center; justify-content: space-between;">
              <div>
                <el-switch v-model="colLevelEnabled" class="row-switch" />
                <span class="row-switch-label">开启列级权限</span>
              </div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <span>其他用户拥有:</span>
                <el-radio-group v-model="colUserPermission" size="small">
                  <el-radio-button value="所有权限" />
                  <el-radio-button value="无任何权限" />
                </el-radio-group>
              </div>
            </div>
            <div class="row-permission-table-panel">
              <el-button type="primary" size="small" class="add-rule-btn" @click="addColRule">添加规则</el-button>
              <el-table :data="colPermissionData" border class="row-permission-table" style="margin-top: 12px;">
                <el-table-column prop="name" label="规则名称" min-width="120" />
                <el-table-column prop="user" label="受限用户" min-width="160" />
                <el-table-column prop="fields" label="限定字段" min-width="180" />
                <el-table-column label="操作" min-width="100">
                  <template #default="scope">
                    <el-link type="primary" @click="editColRule(scope.row, scope.$index)">编辑</el-link>
                    <el-link type="primary" style="margin-left: 8px;" @click="deleteColRule(scope.row, scope.$index)">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
          <template v-else>
            <div style="background:#fff;padding:18px;border-radius:8px;">
              <div style="font-weight:bold;font-size:18px;margin-bottom:12px;">规则名称</div>
              <el-input v-model="colEditForm.name" placeholder="请输入规则名称" style="margin-bottom:16px;width:300px;" />
              <div style="display:flex;gap:24px;">
                <div style="width:260px;border-right:1px solid #eee;padding-right:18px;">
                  <div class="edit-rule-label">编辑规则</div>
                  <el-radio-group v-model="colEditForm.userType" class="user-type-group">
                    <el-radio value="所有人">所有人</el-radio>
                    <el-radio value="指定人">指定人</el-radio>
                  </el-radio-group>
                  <div v-if="colEditForm.userType === '指定人'">
                    <div style="margin-bottom:8px;">
                      <el-tag v-for="(user, idx) in colEditForm.selectedUsers" :key="user" closable @close="removeEditColUser(idx)" style="margin-right:4px;">{{ user }}</el-tag>
                    </div>
                    <el-select v-model="colEditForm.userSelect" placeholder="选择用户" filterable style="width:100%;margin-bottom:8px;">
                      <el-option v-for="item in colUserOptions" :key="item" :label="item" :value="item" />
                    </el-select>
                    <el-checkbox-group v-model="colEditForm.userChecked">
                      <el-checkbox v-for="item in colUserOptions" :key="item" :label="item">{{ item }}</el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
                <div style="flex:1;padding-left:18px;">
                  <div class="edit-rule-label">可见字段</div>
                  <el-checkbox-group v-model="colEditForm.fields">
                    <el-checkbox v-for="f in colFieldOptions" :key="f" :label="f">{{ f }}</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
              <div style="text-align:right;margin-top:24px;">
                <el-button size="small" @click="cancelColEdit">取消</el-button>
                <el-button size="small" type="primary" @click="saveColRule">保存</el-button>
              </div>
            </div>
          </template>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
const props = defineProps({
  show: Boolean
});
const emit = defineEmits(['update:show']);
const drawerShow = ref(props.show);
watch(() => props.show, val => drawerShow.value = val);
watch(drawerShow, val => emit('update:show', val));

// 权限相关数据和方法
const permissionTab = ref('cooperate');
const activeCollapse = ref(['cooperateAuth']);
const rowPermission = ref(false);
const conditionRelation = ref('and');
const conditions = ref(['条件1', '条件2']);
function removeCondition(idx) { conditions.value.splice(idx, 1); }

const rowLevelEnabled = ref(true);
const rowUserPermission = ref('所有权限');
const rowPermissionData = ref([
  { name: '行权限1', user: '全体成员', fields: '年份 煤矿名称' },
  { name: '行权限2', user: '张三、李四', fields: '年份 煤矿名称' },
  { name: '行权限3', user: '用户组1', fields: '年份 煤矿名称' }
]);
const rowEditMode = ref(false);

const colLevelEnabled = ref(true);
const colUserPermission = ref('所有权限');
const colPermissionData = ref([
  { name: '列权限1', user: '全体成员', fields: '年份 煤矿名称' },
  { name: '列权限2', user: '张三、李四', fields: '年份 煤矿名称' },
  { name: '列权限3', user: '用户组1', fields: '年份 煤矿名称' }
]);
const colEditMode = ref(false);

// 行级权限编辑表单相关
const rowFieldOptions = ['柔维度字段', '表指标字段'];
const rowEditForm = ref({
  name: '',
  userType: '所有用户',
  selectedUsers: [],
  userSelect: '',
  userChecked: [],
  conditions: [
    { field: '柔维度字段', filterType: '校举筛选', fixedType: '固定值', fixedValue: '2020,2021,2022' }
  ]
});
let rowEditIndex = null;
function addRowRule() {
  rowEditForm.value = {
    name: '',
    userType: '所有用户',
    selectedUsers: [],
    userSelect: '',
    userChecked: [],
    conditions: [
      { field: '柔维度字段', filterType: '校举筛选', fixedType: '固定值', fixedValue: '2020,2021,2022' }
    ]
  };
  rowEditIndex = null;
  rowEditMode.value = true;
}
function editRowRule(row, idx) {
  rowEditForm.value = JSON.parse(JSON.stringify({
    name: row.name,
    userType: row.userType || '所有用户',
    selectedUsers: row.selectedUsers || [],
    userSelect: '',
    userChecked: row.userChecked || [],
    conditions: row.conditions || [
      { field: '柔维度字段', filterType: '校举筛选', fixedType: '固定值', fixedValue: '2020,2021,2022' }
    ]
  }));
  rowEditIndex = idx;
  rowEditMode.value = true;
}
function saveRowRule() {
  // 组装受限用户和限定字段字符串
  const user = rowEditForm.value.userType === '所有用户' ? '全体成员' : rowEditForm.value.selectedUsers.join('、');
  const fields = rowEditForm.value.conditions.map(c => c.field).join(' ');
  const rule = {
    name: rowEditForm.value.name || '新规则',
    user,
    fields,
    userType: rowEditForm.value.userType,
    selectedUsers: [...rowEditForm.value.selectedUsers],
    userChecked: [...rowEditForm.value.userChecked],
    conditions: JSON.parse(JSON.stringify(rowEditForm.value.conditions))
  };
  if (rowEditIndex !== null) {
    rowPermissionData.value.splice(rowEditIndex, 1, rule);
  } else {
    rowPermissionData.value.push(rule);
  }
  rowEditMode.value = false;
}
function cancelRowEdit() {
  rowEditMode.value = false;
}
function removeEditRowUser(idx) {
  rowEditForm.value.selectedUsers.splice(idx, 1);
}
function addEditCondition() {
  rowEditForm.value.conditions.push({ field: '柔维度字段', filterType: '校举筛选', fixedType: '固定值', fixedValue: '' });
}
function removeEditCondition(idx) {
  rowEditForm.value.conditions.splice(idx, 1);
}
function deleteRowRule(row, idx) {
  rowPermissionData.value.splice(idx, 1);
}

// 普通授权tab相关
const cooperateTab = ref('user');
const userSearch = ref('');
const groupSearch = ref('');
const userList = ref([
  { name: '张三里', permission: 'use' },
  { name: '王五', permission: 'use' },
  { name: '赵德贵', permission: 'use' },
  { name: '五爷', permission: 'use' }
]);
const groupList = ref([
  { name: '研发组', permission: 'use' },
  { name: '广告组', permission: 'use' },
  { name: '市场', permission: 'use' },
  { name: '数据组', permission: 'use' }
]);
const filteredUserList = computed(() => {
  if (!userSearch.value) return userList.value;
  return userList.value.filter(u => u.name.includes(userSearch.value));
});
const filteredGroupList = computed(() => {
  if (!groupSearch.value) return groupList.value;
  return groupList.value.filter(g => g.name.includes(groupSearch.value));
});

// 列级权限编辑表单相关
const colFieldOptions = ['年份(year)', '发电电量', '处理量管理进度', '新增提报及处理进度', '发电单位'];
const colEditForm = ref({
  name: '',
  userType: '所有人',
  selectedUsers: [],
  userSelect: '',
  userChecked: [],
  fields: []
});
let colEditIndex = null;
function addColRule() {
  colEditForm.value = {
    name: '',
    userType: '所有人',
    selectedUsers: [],
    userSelect: '',
    userChecked: [],
    fields: []
  };
  colEditIndex = null;
  colEditMode.value = true;
}
function editColRule(row, idx) {
  colEditForm.value = JSON.parse(JSON.stringify({
    name: row.name,
    userType: row.userType || '所有人',
    selectedUsers: row.selectedUsers || [],
    userSelect: '',
    userChecked: row.userChecked || [],
    fields: row.fieldsArr || []
  }));
  colEditIndex = idx;
  colEditMode.value = true;
}
function saveColRule() {
  const user = colEditForm.value.userType === '所有人' ? '全体成员' : colEditForm.value.selectedUsers.join('、');
  const fields = colEditForm.value.fields.join(' ');
  const rule = {
    name: colEditForm.value.name || '新规则',
    user,
    fields,
    userType: colEditForm.value.userType,
    selectedUsers: [...colEditForm.value.selectedUsers],
    userChecked: [...colEditForm.value.userChecked],
    fieldsArr: [...colEditForm.value.fields]
  };
  if (colEditIndex !== null) {
    colPermissionData.value.splice(colEditIndex, 1, rule);
  } else {
    colPermissionData.value.push(rule);
  }
  colEditMode.value = false;
}
function cancelColEdit() {
  colEditMode.value = false;
}
function removeEditColUser(idx) {
  colEditForm.value.selectedUsers.splice(idx, 1);
}
function deleteColRule(row, idx) {
  colPermissionData.value.splice(idx, 1);
}
</script>

<style scoped>
.permission-collapse {
  margin-top: 16px;
}
.permission-switch-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}
.permission-condition-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}
.permission-condition-list {
  margin-bottom: 8px;
}
.row-permission-panel {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 18px 18px 0 18px;
  margin-top: 8px;
}
.edit-rule-title {
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 12px;
  margin-top: 8px;
}
.edit-rule-content {
  display: flex;
  gap: 24px;
  background: #fff;
  border-radius: 6px;
  padding: 18px 18px 18px 18px;
}
.edit-rule-left {
  width: 220px;
  border-right: 1px solid #eee;
  padding-right: 18px;
}
.edit-rule-label {
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 8px;
}
.user-type-group {
  margin-bottom: 8px;
}
.edit-rule-right {
  flex: 1;
  padding-left: 18px;
}
.edit-rule-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.col-field-tree {
  background: #fafbfc;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 12px;
}
.col-effect-group {
  margin-bottom: 8px;
}
</style> 