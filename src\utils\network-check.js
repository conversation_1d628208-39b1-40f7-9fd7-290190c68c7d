import config from '@/config/index.js'

/**
 * 检查网络连通性
 */
export const checkNetworkConnection = async () => {
  try {
    console.log('检查网络连通性...')
    console.log('目标地址:', config.baseURL)
    
    // 使用fetch进行简单的连通性检查
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000) // 5秒超时
    
    const response = await fetch(config.baseURL, {
      method: 'GET',
      mode: 'no-cors', // 避免CORS问题
      signal: controller.signal
    })
    
    clearTimeout(timeoutId)
    
    console.log('网络连通性检查结果:', {
      status: response.status,
      type: response.type,
      url: response.url
    })
    
    return {
      success: true,
      message: '网络连接正常'
    }
  } catch (error) {
    console.error('网络连通性检查失败:', error)
    
    let message = '网络连接失败'
    if (error.name === 'AbortError') {
      message = '连接超时，请检查网络或服务器状态'
    } else if (error.message) {
      message = error.message
    }
    
    return {
      success: false,
      message: message,
      error: error
    }
  }
}

/**
 * 检查服务器状态
 */
export const checkServerStatus = async () => {
  try {
    console.log('检查服务器状态...')
    
    // 尝试访问一个简单的健康检查端点
    const healthCheckUrl = config.baseURL + 'health'
    
    const response = await fetch(healthCheckUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      return {
        success: true,
        message: '服务器状态正常',
        status: response.status
      }
    } else {
      return {
        success: false,
        message: `服务器响应异常 (状态码: ${response.status})`,
        status: response.status
      }
    }
  } catch (error) {
    console.error('服务器状态检查失败:', error)
    return {
      success: false,
      message: '无法连接到服务器',
      error: error
    }
  }
}
