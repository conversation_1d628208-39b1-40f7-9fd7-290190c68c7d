import { createRouter, createWebHistory } from 'vue-router'
import { notifyRouteChange, isWujieEnvironment } from '@/utils/wujie.js'

const routes = [
  {
    path: '/',
    redirect: '/datasource'
  },
  {
    path: '/datasource',
    name: 'DataSource',
    component: () => import('@/views/dataSource/index.vue'),
    meta: {
      title: '数据源'
    }
  },
  {
    path: '/dataset',
    name: 'DataSet',
    component: () => import('@/views/dataSet/index.vue'),
    meta: {
      title: '数据集'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 智能数据分析平台`
  }
  next()
})

// 路由后置守卫 - 通知主项目路由变化
router.afterEach((to, from) => {
  if (isWujieEnvironment()) {
    notifyRouteChange(to)
  }
})

export default router
