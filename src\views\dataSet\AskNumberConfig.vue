<template>
  <el-drawer v-model="drawerShow" direction="rtl" size="50%" class="ask-number-drawer">
    <template #header>
      <div class="drawer-header">
        <el-switch v-model="askNumberEnabled" @change="handleAskNumberToggle" />
        <span class="header-title">{{ askNumberEnabled ? '开启智能问数' : '关闭智能问数' }}</span>
      </div>
    </template>

    <!-- 当智能问数未开启时显示提示 -->
    <div v-show="!askNumberEnabled" class="disabled-notice">
      <el-empty description="请先开启智能问数功能" :image-size="100">
        <template #description>
          <p class="notice-text">开启智能问数后，您可以配置易混数据和特殊规则</p>
        </template>
      </el-empty>
    </div>

    <div class="ask-number-config-container" v-show="askNumberEnabled">
      <!-- 顶部标签切换 -->
      <div class="config-tabs">
        <div class="config-tab-item" :class="{ active: activeTab === 'easy' }" @click="activeTab = 'easy'">
          易混数据
        </div>
        <div class="config-tab-item" :class="{ active: activeTab === 'special' }" @click="activeTab = 'special'">
          特殊规则
        </div>
      </div>

      <!-- 易混数据配置 -->
      <div v-show="activeTab === 'easy'" class="easy-data-config">
        <!-- 页面头部 -->
        <div class="page-header">
          <div class="header-left">
            <h2 class="page-title">{{ currentRow?.name || '数据集' }} 易混数据配置</h2>
            <div class="description-container">
              <p class="page-description">
                {{ easyConfuseDescription || '暂无描述信息' }}
              </p>
            </div>
          </div>
          <div class="header-right">
            <el-button v-if="askNumberEnabled && filteredDatasetList.length > 0" type="primary" size="large"
              @click="handleSave" :loading="saving" class="save-btn">
              保存此页,并生成易混描述
            </el-button>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
          <!-- 左侧数据集列表 -->
          <div class="left-panel">
            <div class="panel-header">
              <span class="panel-title">已开启问数权限的数据集</span>
            </div>

            <!-- 搜索框 -->
            <div class="search-section">
              <el-input v-model="searchKeyword" placeholder="搜索数据集" prefix-icon="el-icon-search" class="search-input" />
            </div>

            <!-- 数据集列表 -->
            <div class="dataset-list">
              <!-- 加载状态 -->
              <div v-if="loading" class="loading-state">
                <el-icon class="is-loading">
                  <Loading />
                </el-icon>
                <span>加载中...</span>
              </div>

              <!-- 数据集列表 -->
              <div v-else-if="filteredDatasetList.length > 0" class="dataset-item"
                :class="{ active: selectedDatasetId === item.id }" v-for="item in filteredDatasetList" :key="item.id"
                @click="selectDataset(item)">
                <div class="dataset-name">{{ item.name }}</div>
              </div>

              <!-- 空状态 -->
              <div v-else class="empty-state">
                <el-empty :description="searchKeyword ? '未找到匹配的数据集' : '暂无数据集'" :image-size="80" />
              </div>
            </div>
          </div>

          <!-- 右侧配置区域 -->
          <div class="right-panel" v-if="selectedDatasetId">
            <div class="panel-header">
              <div class="header-columns">
                <span class="col-header">业务名称</span>
                <span class="col-header">字段类型</span>
                <span class="col-header">维度选项</span>
                <span class="col-header">操作</span>
              </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="rightPanelLoading" class="loading-state">
              <el-icon class="is-loading">
                <Loading />
              </el-icon>
              <span>加载配置数据中...</span>
            </div>

            <!-- 数据配置表格 -->
            <div v-else class="data-config-table">
              <div class="table-row" v-for="(item, index) in configFieldsData" :key="index">
                <div class="col-field">
                  <span class="field-name">{{ item.businessName || item.fieldName }}</span>
                </div>
                <div class="col-type">
                  <el-tag :type="getFieldTypeColor(item.fieldType)" size="small">
                    {{ getFieldTypeText(item.fieldType) }}
                  </el-tag>
                </div>
                <div class="col-dimension">
                  <!-- 维度类型显示下拉选择框 -->
                  <el-select v-if="item.fieldType === 2" v-model="item.selectedDimensionOptions" multiple
                    placeholder="请选择维度选项" style="width: 100%" size="small"
                    @change="() => handleDimensionChange(item, index)">
                    <template #header>
                      <el-checkbox :indeterminate="getIndeterminate(item)" :model-value="isCheckAll(item)"
                        @change="val => handleCheckAllChange(val, item, index)"
                        style="margin-left: 8px;">全选</el-checkbox>
                    </template>
                    <el-option v-for="option in item.allDimensionOptions" :key="option.value || option"
                      :label="option.label || option" :value="option.value || option" />
                  </el-select>
                  <!-- 非维度类型显示 - -->
                  <span v-else class="no-dimension">-</span>
                </div>
                <div class="col-action">
                  <el-checkbox v-model="item.checkedFlag" :true-label="1" :false-label="0"
                    @change="handleItemToggle(item, index)" />
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧空状态 -->
          <div class="right-panel empty-state" v-else>
            <el-empty description="请选择一个数据集进行配置" />
          </div>
        </div>
      </div>

      <!-- 特殊规则配置 -->
      <div v-show="activeTab === 'special'" class="special-rules-tab">
        <SpecialRulesConfig :current-row="currentRow" />
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <!-- <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </template> -->
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import {
  getAiAskNumberList,
  getAiAskNumberInfo,
  updateAiAskNumberFlag,
  getSelectedDataSetAiAskNumberFields,
  saveSelectedDataSetAiAskNumberFields,
  genAiAskNumberEasyConfuseDesc
} from '@/api/datainfor/dataset.js';
import { ElMessage } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import SpecialRulesConfig from './SpecialRulesConfig.vue';

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  currentRow: {
    type: Object,
    default: () => null
  }
});

// Emits
const emit = defineEmits(['update:show']);

// 响应式数据
const drawerShow = ref(props.show);
const activeTab = ref('easy');
const searchKeyword = ref('');
const askNumberEnabled = ref(false);
const selectedDatasetId = ref(null);
const datasetList = ref([]);
const loading = ref(false);
const rightPanelLoading = ref(false);
const configFieldsData = ref([]);
const easyConfuseDescription = ref('');
const saving = ref(false);

// 监听props变化
watch(() => props.show, (val) => {
  drawerShow.value = val;
  if (val) {
    // 当抽屉打开时，先查询当前状态，然后获取数据集列表
    initAskNumberConfig();
    // 打印当前行的信息
    console.log('当前行信息:', props.currentRow);
    console.log('当前行ID:', props.currentRow?.id);
  }
});

// 监听drawerShow变化
watch(drawerShow, async (val) => {
  emit('update:show', val);
  // 当抽屉打开时，默认选中第一个数据集并加载配置
  if (val && datasetList.value.length > 0 && !selectedDatasetId.value) {
    selectedDatasetId.value = datasetList.value[0].id;
    await getSelectedDataSetFields();
  }
});

// 监听搜索关键词变化，实时搜索
watch(searchKeyword, (newVal) => {
  getDatasetList(newVal);
});

// 初始化问数配置
const initAskNumberConfig = async () => {
  try {
    // 先查询当前状态用于回显
    await getAskNumberInfo();
    // 然后获取数据集列表
    await getDatasetList();
  } catch (error) {
    console.error('初始化问数配置失败:', error);
  }
};

// 查询智能问数信息（用于回显）
const getAskNumberInfo = async () => {
  try {
    if (!props.currentRow?.id) {
      console.warn('缺少数据集ID，无法查询问数信息');
      return;
    }

    const params = {
      dataSetId: props.currentRow.id
    };

    console.log('查询问数信息参数:', params);

    const res = await getAiAskNumberInfo(params);
    console.log('getAiAskNumberInfo API响应:', res);

    // 根据返回的数据设置开关状态
    if (res && typeof res.aiAskNumberFlag !== 'undefined') {
      askNumberEnabled.value = res.aiAskNumberFlag === 1;
      console.log('设置问数开关状态:', askNumberEnabled.value);
    }

  } catch (error) {
    console.error('查询问数信息失败:', error);
    ElMessage.error('查询问数信息失败');
  }
};

// 获取数据集列表
const getDatasetList = async (searchName = '') => {
  try {
    loading.value = true;

    // 构建请求参数
    const params = {
      dataSetId: props.currentRow?.id || null,
      dataSetName: searchName || ''
    };

    console.log('请求参数:', params);
    console.log('使用的数据集ID:', props.currentRow?.id);

    const res = await getAiAskNumberList(params);
    console.log('getAiAskNumberList API响应:', res);

    if (res && Array.isArray(res)) {
      datasetList.value = res;
    } else if (res && res.list && Array.isArray(res.list)) {
      datasetList.value = res.list;
    } else {
      datasetList.value = [];
    }

    // 如果有数据且没有选中的数据集，默认选中第一个并加载其配置
    if (datasetList.value.length > 0 && !selectedDatasetId.value) {
      selectedDatasetId.value = datasetList.value[0].id;
      // 自动加载第一个数据集的配置数据
      await getSelectedDataSetFields();
    }

  } catch (error) {
    console.error('获取数据集列表失败:', error);
    ElMessage.error('获取数据集列表失败');
    datasetList.value = [];
  } finally {
    loading.value = false;
  }
};

// 过滤后的数据集列表（现在主要用于显示，实际搜索通过API完成）
const filteredDatasetList = computed(() => {
  return datasetList.value;
});



// 方法
const getFieldTypeColor = (type) => {
  const colorMap = {
    1: 'success',    // 指标 - 绿色
    2: 'primary',    // 维度 - 蓝色
    3: 'warning'     // 统计周期 - 黄色
  };
  return colorMap[type] || 'info';
};

const getFieldTypeText = (type) => {
  const textMap = {
    1: '指标',
    2: '维度',
    3: '统计周期'
  };
  return textMap[type] || '未知';
};



const selectDataset = (dataset) => {
  selectedDatasetId.value = dataset.id;
  console.log('选择数据集:', dataset);
  console.log('当前选中的数据集ID:', dataset.id);
  console.log('当前选中的数据集名称:', dataset.name);

  // 获取选中数据集的字段配置数据
  getSelectedDataSetFields();
};

// 获取选中数据集的字段配置数据
const getSelectedDataSetFields = async () => {
  try {
    if (!props.currentRow?.id || !selectedDatasetId.value) {
      console.warn('缺少必要的数据集ID');
      return;
    }

    rightPanelLoading.value = true;

    const params = {
      curDataSetId: props.currentRow.id,
      selectedDataSetId: selectedDatasetId.value
    };

    console.log('获取字段配置参数:', params);
    console.log('父组件传递的数据集ID (dataSetId):', props.currentRow.id);
    console.log('左侧选中的数据集ID (selectedDataSetId):', selectedDatasetId.value);

    const res = await getSelectedDataSetAiAskNumberFields(params);
    console.log('getSelectedDataSetAiAskNumberFields API响应:', res);

    if (res && Array.isArray(res)) {
      configFieldsData.value = res;
    } else if (res && res.list && Array.isArray(res.list)) {
      configFieldsData.value = res.list;
    } else {
      configFieldsData.value = [];
    }

  } catch (error) {
    console.error('获取字段配置失败:', error);
    ElMessage.error('获取字段配置失败');
    configFieldsData.value = [];
  } finally {
    rightPanelLoading.value = false;
  }
};

const handleAskNumberToggle = async (value) => {
  console.log('智能问数开关:', value);

  try {
    if (!props.currentRow?.id) {
      ElMessage.error('缺少数据集ID，无法更新状态');
      // 恢复原来的状态
      askNumberEnabled.value = !value;
      return;
    }

    const params = {
      aiAskNumberFlag: value ? 1 : 0,
      dataSetId: props.currentRow.id
    };

    console.log('更新问数状态参数:', params);

    const res = await updateAiAskNumberFlag(params);
    console.log('updateAiAskNumberFlag API响应:', res);

    ElMessage.success(`已${value ? '开启' : '关闭'}智能问数`);

  } catch (error) {
    console.error('更新问数状态失败:', error);
    ElMessage.error('更新问数状态失败');
    // 恢复原来的状态
    askNumberEnabled.value = !value;
  }
};

const handleItemToggle = (item, index) => {
  console.log('切换配置项:', item, index);
};



const handleCancel = () => {
  drawerShow.value = false;
};

const handleSave = async () => {
  try {
    saving.value = true;

    if (!selectedDatasetId.value) {
      ElMessage.warning('请先选择一个数据集');
      return;
    }

    // 步骤1：保存用户选择
    await saveConfigFields();

    // 步骤2：生成易混描述
    await generateEasyConfuseDesc();

    ElMessage.success('保存成功并已生成易混描述');

  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败');
  } finally {
    saving.value = false;
  }
};

// 保存字段配置
const saveConfigFields = async () => {
  try {
    const items = configFieldsData.value.map(item => ({
      checkedFlag: item.checkedFlag || 0,
      fieldId: item.fieldId,
      fieldType: item.fieldType,
      selectedDimensionOptions: item.fieldType === 2 ? (item.selectedDimensionOptions || []) : []
    }));

    const params = {
      dataSetId: props.currentRow.id || '',
      items: items,
      selectedDataSetId: selectedDatasetId.value
    };

    console.log('保存字段配置参数:', params);

    const res = await saveSelectedDataSetAiAskNumberFields(params);
    console.log('saveSelectedDataSetAiAskNumberFields API响应:', res);

  } catch (error) {
    console.error('保存字段配置失败:', error);
    throw error;
  }
};

// 生成易混描述
const generateEasyConfuseDesc = async () => {
  try {
    const params = {
      dataSetId: props.currentRow.id || ''
    };

    console.log('生成易混描述参数:', params);

    const res = await genAiAskNumberEasyConfuseDesc(params);
    console.log('genAiAskNumberEasyConfuseDesc API响应:', res);

    // 更新易混描述文本
    if (res && res.message) {
      easyConfuseDescription.value = res.message;
    } else if (typeof res === 'string') {
      easyConfuseDescription.value = res;
    }

  } catch (error) {
    console.error('生成易混描述失败:', error);
    throw error;
  }
};
// 判断是否全选
const isCheckAll = (item) => {
  const allValues = item.allDimensionOptions.map(opt => opt.value || opt);
  return item.selectedDimensionOptions.length === allValues.length && allValues.length > 0;
};
// 判断是否半选
const getIndeterminate = (item) => {
  const allValues = item.allDimensionOptions.map(opt => opt.value || opt);
  return (
    item.selectedDimensionOptions.length > 0 &&
    item.selectedDimensionOptions.length < allValues.length
  );
};
// 全选/全不选切换
const handleCheckAllChange = (val, item, index) => {
  const allValues = item.allDimensionOptions.map(opt => opt.value || opt);
  if (val) {
    item.selectedDimensionOptions = [...allValues];
  } else {
    item.selectedDimensionOptions = [];
  }
  // 只要有选项被选中，自动勾选 checkedFlag
  item.checkedFlag = item.selectedDimensionOptions.length > 0 ? 1 : 0;
};
// 维度选项变化时自动勾选checkedFlag
const handleDimensionChange = (item, index) => {
  item.checkedFlag = item.selectedDimensionOptions.length > 0 ? 1 : 0;
};
</script>

<script>
export default {
  components: {
    SpecialRulesConfig
  }
}
</script>

<style lang="scss">
// 全局样式，不使用scoped
.ask-number-drawer {
  .el-drawer__header {
    padding: 20px 24px 16px 24px;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 0;
  }

  .el-drawer__body {
    padding: 0 24px 24px 24px;
    height: calc(100% - 60px);
    overflow: hidden;
  }

  .el-drawer__footer {
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
  }
}

.drawer-header {
  display: flex;
  align-items: center;
  gap: 12px;

  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}
</style>

<style scoped lang="scss">
.ask-number-config-container {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.config-tabs {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 20px;
}

.config-tab-item {
  padding: 12px 24px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  color: #606266;
  font-weight: 500;

  &.active {
    color: #409eff;
    border-bottom-color: #409eff;
  }

  &:hover {
    color: #409eff;
  }
}

.easy-data-config {
  flex: 1;
  overflow-y: auto;
}

.config-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.section-description {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  margin: 0;
}

.main-content {
  display: flex;
  height: calc(100% - 100px);
  gap: 16px;
  margin-top: 16px;
}

.left-panel {
  width: 300px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.right-panel {
  flex: 1;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;

  &.empty-state {
    justify-content: center;
    align-items: center;
  }
}

.panel-header {
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
  color: #303133;
}

.panel-title {
  font-size: 14px;
}

.header-columns {
  display: flex;
  align-items: center;
}

.col-header {
  font-size: 14px;
  color: #909399;

  &:first-child {
    flex: 1;
  }

  &:nth-child(2) {
    width: 100px;
    text-align: center;
  }

  &:nth-child(3) {
    width: 200px;
    text-align: center;
  }

  &:last-child {
    width: 80px;
    text-align: center;
  }
}

.search-section {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

.search-input {
  width: 100%;
}

.dataset-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  font-size: 14px;

  .el-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.dataset-item {
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: #f5f7fa;
  }

  &.active {
    background: #ecf5ff;
    color: #409eff;
    border: 1px solid #b3d8ff;
  }
}

.dataset-name {
  font-size: 14px;
}

.data-config-table {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
}

.table-row {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }
}

.col-field {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.col-type {
  width: 100px;
  text-align: center;
}

.col-dimension {
  width: 200px;
  text-align: center;
  padding: 0 8px;
}

.col-action {
  width: 80px;
  text-align: center;
}

.no-dimension {
  color: #c0c4cc;
  font-size: 14px;
}

.field-name {
  font-size: 14px;
  color: #303133;
}

.field-type-tag {
  margin-left: 8px;
}

.special-rules-config {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.coming-soon {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0 0 0;
  border-top: 1px solid #e4e7ed;
}

.special-rules-tab {
  height: 100%;
  overflow-y: auto;
}

// 易混数据配置页面样式
.easy-data-config {
  .page-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;

    .header-left {
      flex: 1;
    }

    .header-right {
      margin-left: 24px;
      display: flex;
      align-items: center;
    }

    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 8px 0;
      letter-spacing: 0.5px;
    }

    .description-container {
      margin-top: 12px;
      padding: 16px;
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border: 1px solid #bae6fd;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(14, 165, 233, 0.1);
    }

    .page-description {
      font-size: 16px;
      color: #0369a1;
      margin: 0;
      line-height: 1.6;
      font-weight: 500;
      text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    }

    .save-btn {
      width: 240px;
      height: 40px;
      font-size: 14px;
      font-weight: 600;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

// 禁用状态提示样式
.disabled-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #ebeef5;

  .notice-text {
    color: #909399;
    font-size: 16px;
    margin-top: 16px;
  }
}
</style>
