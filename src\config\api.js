import request from './request.js'

/**
 * 封装GET请求
 * @param {string} url 请求地址
 * @param {object} params 请求参数
 * @param {object} config 额外配置
 */
export const get = (url, params = {}, config = {}) => {
  return request({
    method: 'GET',
    url,
    params,
    ...config
  })
}

/**
 * 封装POST请求
 * @param {string} url 请求地址
 * @param {object} data 请求数据
 * @param {object} config 额外配置
 */
export const post = (url, data = {}, config = {}) => {
  return request({
    method: 'POST',
    url,
    data,
    ...config
  })
}

/**
 * 封装PUT请求
 * @param {string} url 请求地址
 * @param {object} data 请求数据
 * @param {object} config 额外配置
 */
export const put = (url, data = {}, config = {}) => {
  return request({
    method: 'PUT',
    url,
    data,
    ...config
  })
}

/**
 * 封装DELETE请求
 * @param {string} url 请求地址
 * @param {object} params 请求参数
 * @param {object} config 额外配置
 */
export const del = (url, params = {}, config = {}) => {
  return request({
    method: 'DELETE',
    url,
    params,
    ...config
  })
}

/**
 * 封装PATCH请求
 * @param {string} url 请求地址
 * @param {object} data 请求数据
 * @param {object} config 额外配置
 */
export const patch = (url, data = {}, config = {}) => {
  return request({
    method: 'PATCH',
    url,
    data,
    ...config
  })
}

// 导出request实例，供特殊情况使用
export { request }

export default {
  get,
  post,
  put,
  delete: del,
  patch,
  request
}
