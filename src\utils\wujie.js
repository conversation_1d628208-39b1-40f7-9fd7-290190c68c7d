/**
 * 无界微前端工具函数
 * 处理与主项目的通信和数据交换
 */

/**
 * 检查是否在无界环境中运行
 * @returns {boolean} 是否在无界环境
 */
export const isWujieEnvironment = () => {
  return !!(window.__WUJIE || window.$wujie)
}

/**
 * 获取无界实例
 * @returns {Object|null} 无界实例
 */
export const getWujieInstance = () => {
  return window.$wujie || window.__WUJIE || null
}

/**
 * 获取主项目传递的props
 * @returns {Object} props对象
 */
export const getMainAppProps = () => {
  const wujie = getWujieInstance()
  return wujie?.props || {}
}

/**
 * 从主项目props获取用户信息
 * @returns {Object|null} 用户信息
 */
export const getUserInfoFromProps = () => {
  const props = getMainAppProps()
  return props.userInfo || props.user || null
}

/**
 * 从主项目props获取token
 * @returns {string|null} token
 */
export const getTokenFromProps = () => {
  const props = getMainAppProps()
  return props.token || props.accessToken || null
}

/**
 * 从主项目props获取权限信息
 * @returns {Array|null} 权限列表
 */
export const getPermissionsFromProps = () => {
  const props = getMainAppProps()
  return props.permissions || props.roles || null
}

/**
 * 从主项目props获取租户信息
 * @returns {string|null} 租户ID
 */
export const getTenantIdFromProps = () => {
  const props = getMainAppProps()
  return props.tenantId || props.tenant || null
}

/**
 * 向主项目发送事件
 * @param {string} eventName 事件名称
 * @param {any} data 事件数据
 */
export const emitToMainApp = (eventName, data) => {
  if (!isWujieEnvironment()) {
    console.warn('不在无界环境中，无法发送事件到主项目')
    return
  }

  try {
    const wujie = getWujieInstance()
    
    // 方式1: 使用无界的事件总线
    if (wujie?.bus) {
      wujie.bus.$emit(eventName, data)
    }
    
    // 方式2: 使用postMessage
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'MICRO_APP_EVENT',
        eventName,
        data,
        source: 'data-management-module',
        timestamp: Date.now()
      }, '*')
    }
    
    console.log(`向主项目发送事件: ${eventName}`, data)
  } catch (error) {
    console.error('向主项目发送事件失败:', error)
  }
}

/**
 * 监听主项目事件
 * @param {string} eventName 事件名称
 * @param {Function} callback 回调函数
 */
export const listenMainAppEvent = (eventName, callback) => {
  if (!isWujieEnvironment()) {
    console.warn('不在无界环境中，无法监听主项目事件')
    return
  }

  try {
    const wujie = getWujieInstance()
    
    // 方式1: 监听无界事件总线
    if (wujie?.bus) {
      wujie.bus.$on(eventName, callback)
    }
    
    // 方式2: 监听postMessage
    const messageHandler = (event) => {
      if (event.data?.type === 'MAIN_APP_EVENT' && event.data?.eventName === eventName) {
        callback(event.data.data)
      }
    }
    
    window.addEventListener('message', messageHandler)
    
    // 返回取消监听的函数
    return () => {
      if (wujie?.bus) {
        wujie.bus.$off(eventName, callback)
      }
      window.removeEventListener('message', messageHandler)
    }
  } catch (error) {
    console.error('监听主项目事件失败:', error)
  }
}

/**
 * 通知主项目子应用已准备就绪
 */
export const notifyMainAppReady = () => {
  emitToMainApp('CHILD_APP_READY', {
    appName: 'data-management-module',
    version: '1.0.0',
    timestamp: Date.now()
  })
}

/**
 * 通知主项目路由变化
 * @param {Object} route 路由信息
 */
export const notifyRouteChange = (route) => {
  emitToMainApp('ROUTE_CHANGE', {
    path: route.path,
    name: route.name,
    params: route.params,
    query: route.query
  })
}

/**
 * 请求主项目刷新token
 */
export const requestTokenRefresh = () => {
  emitToMainApp('REQUEST_TOKEN_REFRESH', {
    reason: 'token_expired',
    timestamp: Date.now()
  })
}

/**
 * 通知主项目发生错误
 * @param {Error} error 错误对象
 * @param {string} context 错误上下文
 */
export const notifyError = (error, context = '') => {
  emitToMainApp('CHILD_APP_ERROR', {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: Date.now()
  })
}

/**
 * 获取主项目的配置信息
 * @returns {Object} 配置信息
 */
export const getMainAppConfig = () => {
  const props = getMainAppProps()
  return props.config || props.settings || {}
}

/**
 * 检查主项目是否支持某个功能
 * @param {string} feature 功能名称
 * @returns {boolean} 是否支持
 */
export const isFeatureSupported = (feature) => {
  const config = getMainAppConfig()
  return config.features?.[feature] === true
}

/**
 * 获取主项目的API基础URL
 * @returns {string|null} API基础URL
 */
export const getMainAppApiBaseUrl = () => {
  const config = getMainAppConfig()
  return config.apiBaseUrl || config.baseURL || null
}

/**
 * 初始化无界通信
 * 设置必要的事件监听器
 */
export const initWujieComm = () => {
  if (!isWujieEnvironment()) {
    console.log('不在无界环境中，跳过无界通信初始化')
    return
  }

  console.log('初始化无界微前端通信...')
  
  // 监听主项目的用户信息更新
  listenMainAppEvent('USER_INFO_UPDATE', (userInfo) => {
    console.log('收到主项目用户信息更新:', userInfo)
    // 可以在这里触发应用内的用户信息更新事件
    window.dispatchEvent(new CustomEvent('userInfoUpdate', { detail: userInfo }))
  })
  
  // 监听主项目的token更新
  listenMainAppEvent('TOKEN_UPDATE', (token) => {
    console.log('收到主项目token更新')
    // 可以在这里触发应用内的token更新事件
    window.dispatchEvent(new CustomEvent('tokenUpdate', { detail: token }))
  })
  
  // 监听主项目的权限更新
  listenMainAppEvent('PERMISSIONS_UPDATE', (permissions) => {
    console.log('收到主项目权限更新:', permissions)
    window.dispatchEvent(new CustomEvent('permissionsUpdate', { detail: permissions }))
  })
  
  // 通知主项目子应用已准备就绪
  setTimeout(() => {
    notifyMainAppReady()
  }, 100)
}

/**
 * 销毁无界通信
 */
export const destroyWujieComm = () => {
  if (!isWujieEnvironment()) {
    return
  }
  
  console.log('销毁无界微前端通信...')
  // 这里可以添加清理逻辑
}

export default {
  isWujieEnvironment,
  getWujieInstance,
  getMainAppProps,
  getUserInfoFromProps,
  getTokenFromProps,
  getPermissionsFromProps,
  getTenantIdFromProps,
  emitToMainApp,
  listenMainAppEvent,
  notifyMainAppReady,
  notifyRouteChange,
  requestTokenRefresh,
  notifyError,
  getMainAppConfig,
  isFeatureSupported,
  getMainAppApiBaseUrl,
  initWujieComm,
  destroyWujieComm
}
