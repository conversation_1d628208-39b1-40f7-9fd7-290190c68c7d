import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import './styles/index.scss'
import { initWujieComm, isWujieEnvironment } from '@/utils/wujie.js'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(router)
app.use(ElementPlus)

// 挂载应用
const mountApp = () => {
  app.mount('#app')

  // 如果在无界环境中，初始化通信
  if (isWujieEnvironment()) {
    console.log('检测到无界微前端环境，初始化通信...')
    initWujieComm()
  } else {
    console.log('独立运行模式')
  }
}

// 检查是否在无界环境中
if (window.__WUJIE_MOUNT__) {
  // 在无界环境中，等待挂载信号
  window.__WUJIE_MOUNT__ = mountApp
} else {
  // 独立运行，直接挂载
  mountApp()
}
