<template>
  <div class="dataset-main-layout">
    <!-- 左侧数据源列表 -->
    <div class="dataset-tree-panel" :class="{ 'edit-mode': isEditMode, 'from-data-source': isFromDataSource }">
      <div class="tree-header">{{ isEditMode ? '数据集修改' : (isFromDataSource ? '从数据源创建数据集' : '数据集编辑') }}</div>

      <!-- 数据源选择下拉框 -->
      <div class="source-select">
        <el-select v-model="selectedSource" placeholder="请选择数据源" class="source-select-input"
          :disabled="isEditMode || isFromDataSource"
          @click="!isEditMode && !isFromDataSource && (showSourceDialog = true)">
          <el-option v-if="selectedSource" :label="sourceDisplayName" :value="selectedSource" />
        </el-select>
        <div v-if="isFromDataSource" class="source-tip">
          <el-icon>
            <InfoFilled />
          </el-icon>
          <span>已从数据源页面跳转，数据源已锁定</span>
        </div>
      </div>
      <div>选择数据表</div>
      <div class="data-table-list">
        <div v-for="table in tableList" :key="table.id" class="table-item" :class="{
          'active': selectedTable?.tableProp == table.tableProp,
          'clickable': !isEditMode || isFromDataSource,
          'disabled': isEditMode && !isFromDataSource
        }" @click="(!isEditMode || isFromDataSource) && fetchTableOrSheetDetail(table)">
          <div class="table-name">{{ table.tableProp || '暂无名称' }}</div>
          <div class="table-info">
            <span class="table-type"> 状态:{{ table.dataStatus }}</span>
          </div>
        </div>
      </div>

      <!-- 数据源选择弹窗 -->
      <el-dialog v-model="showSourceDialog" title="选择数据源" width="500px" :close-on-click-modal="false">
        <div class="source-dialog-content">
          <!-- 搜索框 -->
          <div class="search-box">
            <el-input v-model="sourceSearchKeyword" placeholder="请输入数据源名称" prefix-icon="el-icon-search" clearable />
          </div>
          <!-- 数据源列表 -->
          <div class="source-list-container">
            <div v-for="item in filteredSourceList" :key="item.id" class="source-list-item"
              :class="{ 'active': tempSelectedSource?.id == item.id }" @click="handleSourceSelect(item)">
              <div class="source-name">{{ item.name || '暂无名称' }}</div>
              <div class="source-info">
                <span class="source-type">{{ getSourceTypeName(item.type) }}</span>
                <span class="source-creator">{{ item.creator }}</span>
              </div>
            </div>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showSourceDialog = false">取消</el-button>
            <el-button type="primary" @click="confirmSourceSelect">确定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
    <!-- 右侧内容区 -->
    <div class="dataset-content-panel">
      <div class="dataset-header-bar">
        <span class="dataset-header-title">{{ isEditMode ? '修改数据集' : (isFromDataSource ? '从数据源创建数据集' : '新建数据集')
        }}</span>
        <div>
          <el-button type="primary" @click="showPermission = true">配置权限</el-button>
          <el-button type="primary" class="save-btn" @click="createDataSet">{{ isEditMode ? '修改' : '保存' }}</el-button>
        </div>
      </div>
      <el-tabs v-model="activeTab" class="dataset-tabs">

        <el-tab-pane label="字段配置" name="fields"></el-tab-pane>
        <el-tab-pane label="数据预览" name="preview">
          <DataPreview :tableData="tableData" :getDataSourceId="getDataSourceId" :selectedTable="selectedTable"
            ref="dataPreviewRef" />
        </el-tab-pane>
      </el-tabs>
      <!-- 字段配置表格 -->
      <div v-if="activeTab === 'fields'" class="dataset-table-panel">
        <el-table :data="tableData" stripe border class="dataset-table">
          <el-table-column prop="name" label="字段原名" min-width="100" />
          <el-table-column prop="comment" label="字段备注" min-width="100" />
          <el-table-column prop="type" label="数据类型" min-width="120">
            <template #default="scope">
              <el-select v-model="scope.row.type" placeholder="请选择" style="width: 100%">
                <el-option label="文本" value="文本" />
                <el-option label="数值" value="数值" />
                <el-option label="日期" value="日期" />
                <el-option label="日期时间" value="日期时间" />
                <el-option label="时间" value="时间" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="stdName" label="业务名称" min-width="100">
            <template #default="scope">
              <div class="business-name-cell">
                <span class="business-name-text">{{ scope.row.stdName || '请选择业务名称' }}</span>
                <el-icon class="edit-icon" @click="openStdNameDialog(scope.row)">
                  <EditPen />
                </el-icon>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="descriptionValue" label="字段描述" min-width="120" show-overflow-tooltip>
            <template #default="scope">
              <span class="field-description" :title="scope.row.descriptionValue">
                {{ scope.row.descriptionValue || '暂无描述' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="isIndex" label="字段类型" min-width="120">
            <template #default="scope">
              <div class="field-type-cell">
                <el-tag v-if="scope.row.isIndex" :color="getFieldTypeColorByText(scope.row.isIndex)"
                  style="color: white;">
                  {{ scope.row.isIndex }}
                </el-tag>
                <span v-else class="empty-field-type">-</span>
                <el-icon v-if="scope.row.isIndex === '维度'" class="dimension-arrow"
                  @click="showDimensionOptions(scope.row)">
                  <ArrowDown />
                </el-icon>
              </div>
            </template>
          </el-table-column>



          <el-table-column label="操作" min-width="100">
            <template #default="scope">
              <el-button v-if="scope.row.showFieldFlag === 1" type="success" size="small"
                @click="scope.row.showFieldFlag = 0">
                显示
              </el-button>
              <el-button v-else type="info" size="small" @click="scope.row.showFieldFlag = 1">
                隐藏
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 权限分配抽屉 -->
    <PermissionConfig v-model:show="showPermission" />


    <!-- 保存数据集弹窗 -->
    <el-dialog v-model="showSaveDialog" :title="isEditMode ? '修改数据集' : '保存数据集'" width="500px"
      :close-on-click-modal="false">
      <el-form label-width="100px">
        <el-form-item label="数据集名称">
          <el-input v-model="datasetName" placeholder="请输入数据集名称" />
        </el-form-item>
        <el-form-item label="数据集描述">
          <el-input v-model="datasetDescription" type="textarea" :rows="5" placeholder="请一句话描述核心统计周期、指标、和维度
示例：该数据集存储了每个月份各个煤款的xxx、xxx、xxx、指标。" />
          <!-- <el-button type="primary" size="small" style="margin-top: 8px;" @click="generateDescription">自动生成</el-button>  -->
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showSaveDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSaveDataSet">{{ isEditMode ? '修改' : '保存' }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 业务名称编辑弹窗 -->
    <el-dialog v-model="stdNameDialogVisible" title="修改业务名称" width="800px">
      <div style="margin-bottom: 12px;">字段原名：{{ stdNameEditRow.name }}</div>
      <div style="margin-bottom: 12px;">字段注释：{{ stdNameEditRow.comment }}</div>
      <el-input v-model="stdNameSearch" placeholder="搜索业务名称" prefix-icon="el-icon-search" style="margin-bottom: 12px;"
        @input="handleSearchChange" />

      <!-- 表格显示业务名称数据 -->
      <el-table :data="stdNameOptions" style="width: 100%; margin-bottom: 16px;" max-height="400px"
        @row-click="handleBusinessNameSelect" highlight-current-row :current-row-key="selectedBusinessRowKey">
        <el-table-column prop="businessName" label="业务名称" min-width="120" />
        <el-table-column prop="type" label="字段类型" min-width="100">
          <template #default="scope">
            <el-tag :color="getFieldTypeColor(scope.row.type)" style="color: white;">
              {{ getFieldTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="termDefinitions" label="字段描述" min-width="200" show-overflow-tooltip />
      </el-table>

      <template #footer>
        <el-button @click="stdNameDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveStdName">保存</el-button>
      </template>
    </el-dialog>

    <!-- 维度枚举弹窗 -->
    <el-dialog v-model="dimensionDialogVisible" title="维度枚举项目" width="600px">
      <div>
        <span style="margin-bottom: 12px;">字段名称：{{ currentDimensionField.name }}</span>
        <span style="margin-bottom: 12px;">字段类型：维度</span>
        <span style="margin-bottom: 12px;">枚举值总数：{{ dimensionOptions.length }}</span>
      </div>

      <div class="dimension-list-container">
        <span class="dimension-item" v-for="(item, index) in dimensionOptions" :key="index">
          {{ item }}
        </span>
      </div>

      <template #footer>
        <el-button @click="dimensionDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { getDataSourceDetail, postCreateDataSet, postUpdateDataSet, getDataSetDetail, getBusinessName, getDimensionOptions } from '@/api/datainfor/dataset'
import { getDataSourceList, getDataSourceWithTables } from '@/api/datainfor/dataupload'
import { ElMessage } from 'element-plus';
import DataPreview from './dataPreview.vue';
import { useRoute, useRouter } from 'vue-router';
import PermissionConfig from './PermissionConfig.vue';

import { InfoFilled, EditPen, ArrowDown } from '@element-plus/icons-vue';
const route = useRoute();
const router = useRouter();

// 数据源列表
const dataSourceList = ref([]);
const selectedSource = ref(null);
const tableList = ref([]);
const selectedTable = ref({ id: '', tableProp: '' });

// 数据源选择相关
const showSourceDialog = ref(false);
const sourceSearchKeyword = ref('');
const tempSelectedSource = ref(null);
const getDataSourceId = ref('');

// 是否为修改模式
const isEditMode = ref(false);
// 当前编辑的数据集ID
const currentDatasetId = ref(null);
// 是否从数据源页面跳转
const isFromDataSource = ref(false);

// 业务名称编辑弹窗相关
const stdNameDialogVisible = ref(false);
const stdNameEditRow = ref({});
const stdNameSearch = ref('');
const stdNameOptionsRaw = ref([]); // 改为响应式数据，从接口获取
const stdNameOptions = computed(() => {
  if (!stdNameSearch.value) return stdNameOptionsRaw.value;
  return stdNameOptionsRaw.value.filter(item =>
    item.businessName.includes(stdNameSearch.value) ||
    item.termDefinitions.includes(stdNameSearch.value)
  );
});
const stdNameSelected = ref('');
const stdNameDefinition = ref('');
const selectedBusinessRowKey = ref(null);

// 维度枚举相关
const dimensionDialogVisible = ref(false);
const dimensionOptions = ref([]);
const currentDimensionField = ref({});

onMounted(async () => {
  await getDataSourceListData();
  // 检查是否为修改模式
  await checkEditMode();
  // 检查是否从数据源页面跳转
  await checkFromDataSource();
});

// 检查是否从数据源页面跳转
const checkFromDataSource = async () => {
  const query = route.query;
  if (query.dataSourceId && query.tableProp) {
    // 从数据源页面跳转过来
    isFromDataSource.value = true;

    try {
      // 1. 先获取数据源列表
      await getDataSourceListData();

      // 2. 获取当前数据源详情
      const res = await getDataSourceWithTables(query.dataSourceId);
      if (res) {
        // 设置数据源信息
        getDataSourceId.value = query.dataSourceId;
        selectedSource.value = {
          id: query.dataSourceId,
          name: res.name,
          type: res.type,
          creator: res.creator
        };

        // 3. 设置数据表列表
        if (res.dataTablePageList) {
          tableList.value = res.dataTablePageList.list || [];

          // 4. 查找并选中指定的数据表
          const targetTable = tableList.value.find(table => table.tableProp === query.tableProp);

          if (targetTable) {
            selectedTable.value = {
              ...targetTable,
              tableLabel: query.tableLabel,
              tableProp: query.tableProp,
              dataSourceId: query.dataSourceId
            };

            // 5. 获取数据表字段详情
            // 根据数据源类型决定 tableNameOrSheetId 参数
            // 如果是EXCEL文件类型（本地文件数据源），传递数据表ID
            // 如果是数据库类型数据源，传递表名
            const tableNameOrSheetId = targetTable.id ? targetTable.id : query.tableProp;

            const params = {
              dataSourceId: query.dataSourceId,
              tableNameOrSheetId: tableNameOrSheetId
            };

            const fieldDetail = await getDataSourceDetail(params);
            if (fieldDetail) {
              tableData.value = fieldDetail.map(item => ({
                name: item.fieldName, // 字段原名
                comment: item.fieldLabel, // 字段备注
                type: getFieldDataTypeName(item.dataType), // 数据类型
                isIndex: '', // 字段类型默认为空
                stdName: item.businessName, // 业务名称
                descriptionValue: item.description, // 字段描述
                dimensionOptionsValue: item.dimensionOptions, // 维度选项
                showFieldFlag: 0, // 默认隐藏
                id: item.id // 主键id
              }));
            }
          }
        }
      }
    } catch (error) {
      console.error('初始化数据失败:', error);
      ElMessage.error('初始化数据失败');
    }
  }
};

// 检查是否为修改模式
const checkEditMode = async () => {
  const query = route.query;
  if (query.id) {
    // 有ID参数，说明是修改模式
    isEditMode.value = true;
    currentDatasetId.value = query.id;

    // 设置数据源信息
    if (query.datasourceId) {
      getDataSourceId.value = query.datasourceId;
      // 获取数据源详情并设置选中状态
      await loadDataSourceInfo(query.datasourceId);
    }

    // 设置数据表信息
    if (query.tableNameOrSheetId) {
      await loadTableInfo(query.tableNameOrSheetId);
    }

    // 加载数据集详情
    await loadDatasetDetail(query.id);
  }
};

// 加载数据源信息
const loadDataSourceInfo = async (datasourceId) => {
  try {
    // 从数据源列表中查找对应的数据源
    const source = dataSourceList.value.find(item => item.id == datasourceId);
    if (source) {
      selectedSource.value = source;
      // 获取该数据源下的数据表列表
      const res = await getDataSourceWithTables(datasourceId);
      console.log('数据源下的数据表列表:', res);

      if (res.dataTablePageList) {
        tableList.value = res.dataTablePageList.list || [];
      }
    }
  } catch (error) {
    console.error('加载数据源信息失败:', error);
  }
};

// 加载数据表信息
const loadTableInfo = async (tableNameOrSheetId) => {
  try {
    // 从数据表列表中查找对应的数据表
    const table = tableList.value.find(item =>
      item.id == tableNameOrSheetId || item.tableProp == tableNameOrSheetId
    );
    if (table) {
      selectedTable.value = table;
      // 获取数据表字段详情
      await fetchTableOrSheetDetail(table);
    }
  } catch (error) {
    console.error('加载数据表信息失败:', error);
  }
};

// 加载数据集详情
const loadDatasetDetail = async (datasetId) => {
  try {
    const res = await getDataSetDetail({ dataSetId: datasetId });
    console.log('数据集详情API响应:', res);

    if (res) {
      // 设置数据集基本信息
      datasetName.value = res.name || '';
      datasetDescription.value = res.description || '';

      // 优先使用 dataSetFieldRespVOS，如果没有则使用 dataSetFieldSaveReqVOS
      const fieldData = res.dataSetFieldRespVOS || res.dataSetFieldSaveReqVOS;

      if (fieldData && fieldData.length > 0) {
        tableData.value = fieldData.map(item => ({
          name: item.fieldName, // 字段原名
          comment: item.fieldLabel, // 字段备注
          type: getFieldDataTypeName(item.dataType), // 数据类型
          isIndex: item.businessName ? getFieldTypeName(item.fieldType) : '', // 有业务名称时才显示字段类型
          stdName: item.businessName, // 业务名称
          descriptionValue: item.description, // 字段描述
          dimensionOptionsValue: item.dimensionOptions, // 维度选项
          showFieldFlag: item.showFieldFlag !== undefined ? item.showFieldFlag : 0, // 显示状态，默认为1
          id: item.id // 主键id
        }));

        console.log('回显的表格数据:', tableData.value);
      } else {
        console.log('没有找到字段数据，检查API响应结构');
      }
    }
  } catch (error) {
    console.error('加载数据集详情失败:', error);
    ElMessage.error('加载数据集详情失败');
  }
};

// 过滤后的数据源列表
const filteredSourceList = computed(() => {
  console.log(dataSourceList.value, 'dataSourceList//////////////////5');

  if (!sourceSearchKeyword.value) return dataSourceList.value;
  return dataSourceList.value.filter(item =>
    item.name.toLowerCase().includes(sourceSearchKeyword.value.toLowerCase())
  );
});

// 获取数据源类型名称
const getSourceTypeName = (type) => {
  const typeMap = {
    1: '本地数据源',
    2: '数据库数据源',
    3: 'API数据源'
  };
  return typeMap[type] || '未知类型';
};

// 获取字段数据类型名称
const getFieldDataTypeName = (type) => {
  const typeMap = {
    1: '文本',
    2: '数值',
    3: '日期',
    4: '日期时间',
    5: '时间'
  };
  return typeMap[type] || '文本'; // 默认值
};

// 获取字段维度/指标类型名称
const getFieldTypeName = (type) => {
  const typeMap = {
    1: '指标',
    2: '维度',
    3: '统计周期'
  };
  return typeMap[type] || '指标'; // 默认值
};

// 获取数据类型数字值
const getDataTypeNumber = (type) => {
  const typeMap = {
    '文本': 1,
    '数值': 2,
    '日期': 3,
    '日期时间': 4,
    '时间': 5
  };
  return typeMap[type] || 1; // 默认文本
};

// 获取字段类型数字值 (维度/指标/统计周期)
const getFieldTypeNumber = (type) => {
  const typeMap = {
    '指标': 1,
    '维度': 2,
    '统计周期': 3
  };
  return typeMap[type] || 1; // 默认指标
};

// 处理数据源选择
const handleSourceSelect = (source) => {
  console.log(source, 'source55555555');

  tempSelectedSource.value = source;
};

// 确认选择数据源
const confirmSourceSelect = (aaaaa) => {
  console.log(aaaaa, 'aaaaa55555');

  if (tempSelectedSource.value) {
    selectedSource.value = tempSelectedSource.value;
    getDataSourceId.value = selectedSource.value?.id;
    // 获取该数据源下的数据表列表
    getDataSourceWithTables(selectedSource.value.id).then(res => {
      console.log("数据源=>数据表列表55555", res);

      if (res.dataTablePageList) {
        tableList.value = res?.dataTablePageList?.list || [];
      }
    });
  }
  showSourceDialog.value = false;
};

// 选择数据表或者sheet表单
const fetchTableOrSheetDetail = async (table) => {
  selectedTable.value = table;

  // 根据数据源类型决定 tableNameOrSheetId 参数
  // 如果是EXCEL文件类型（本地文件数据源），传递数据表ID
  // 如果是数据库类型数据源，传递表名
  const tableNameOrSheetId = table.id ? table.id : table.tableProp;

  console.log('获取表格详情参数:', {
    dataSourceId: getDataSourceId.value,
    tableNameOrSheetId: tableNameOrSheetId,
    dataSourceType: table.id ? 'EXCEL文件类型' : '数据库类型'
  });

  const params = {
    dataSourceId: getDataSourceId.value,
    tableNameOrSheetId: tableNameOrSheetId
  };

  try {
    const res = await getDataSourceDetail(params);
    console.log('获取到的表格字段详情:', res);
    // 处理获取到的表格数据
    if (res) {
      tableData.value = res.map(item => ({
        name: item.fieldName, // 字段原名
        comment: item.fieldLabel, // 字段备注
        type: getFieldDataTypeName(item.dataType), // 数据类型
        isIndex: '', // 字段类型默认为空
        stdName: item.businessName, // 业务名称
        descriptionValue: item.description, // 字段描述
        dimensionOptionsValue: item.dimensionOptions, // 维度选项
        showFieldFlag: 0, // 默认显示
        id: item.id // 主键id
      }));
    } else {
      tableData.value = [];
    }
  } catch (err) {
    console.error('获取表格数据失败:', err);
    tableData.value = [];
    ElMessage.error('获取表格数据失败');
  }
};



// 获取数据源列表
const getDataSourceListData = async () => {
  try {
    const res = await getDataSourceList();
    console.log('数据源列表:', res);
    // 合并所有数据源到一个数组
    dataSourceList.value = [
      ...(res.Excel || []),
      ...(res.API || []),
      ...(res.DataBase || [])
    ];
  } catch (error) {
    console.error('获取数据源列表失败:', error);
  }
};

// 创建数据集
const createDataSet = () => {
  showSaveDialog.value = true;
};

// tab切换
const activeTab = ref('fields');

// 表格数据
const tableData = ref([]);

// 权限分配抽屉
const showPermission = ref(false);




// 新增的表单相关
const showSaveDialog = ref(false);
const datasetName = ref('');
const datasetDescription = ref('');

// 自动生成数据集描述
// const generateDescription = () => {
//   let description = '';
//   if (datasetName.value) {
//     description += `数据集名称为"${datasetName.value}"。`;
//   }
//   if (selectedTable.value && selectedTable.value.tableLabel) {
//     description += `该数据集来源于数据表"${selectedTable.value.tableLabel}"。`;
//   } else if (selectedTable.value && selectedTable.value.tableName) {
//     description += `该数据集来源于数据表"${selectedTable.value.tableName}"。`;
//   }
//   if (tableData.value && tableData.value.length > 0) {
//     const fieldNames = tableData.value.map(field => field.comment || field.name).filter(Boolean);
//     if (fieldNames.length > 0) {
//       description += `包含字段：${fieldNames.join('、')}。`;
//     }
//   }

//   if (!description) {
//     description = '这是一个自动生成的数据集描述。';
//   }
//   datasetDescription.value = description;
// };

// 处理保存数据集的逻辑
const handleSaveDataSet = async () => {
  if (!datasetName.value) {
    ElMessage.error('请输入数据集名称');
    return;
  }
  if (!selectedTable.value || !getDataSourceId.value) {
    ElMessage.error('请先选择数据源和数据表');
    return;
  }

  const dataSetFieldSaveReqVOS = tableData.value.map((item, index) => ({
    businessName: item.stdName, // 业务名称
    dataType: getDataTypeNumber(item.type), // 数据类型
    datasetId: isEditMode.value ? currentDatasetId.value : 0, // 修改时使用当前数据集ID，新增时为0
    description: item.descriptionValue, // 字段描述
    // dimensionOptions: item.dimensionOptionsValue, // 维度选项
    fieldIndex: index, // 字段序号
    fieldLabel: item.comment, // 字段备注
    fieldName: item.name, // 字段原名
    fieldType: getFieldTypeNumber(item.isIndex), // 维度/指标
    showFieldFlag: item.showFieldFlag, // 显示状态
    id: item.id || undefined // 修改时保留字段ID，新增时不传
  }));

  // 根据数据源类型决定 tableNameOrSheetId 参数
  // 如果是EXCEL文件类型（本地文件数据源），传递数据表ID
  // 如果是数据库类型数据源，传递表名
  const tableNameOrSheetId = selectedTable.value.id ? selectedTable.value.id : selectedTable.value.tableProp;

  const payload = {
    dataSetFieldSaveReqVOS: dataSetFieldSaveReqVOS,
    datasourceId: getDataSourceId.value,
    description: datasetDescription.value,
    name: datasetName.value,
    tableNameOrSheetId: tableNameOrSheetId
  };

  // 如果是修改模式，添加数据集ID
  if (isEditMode.value) {
    payload.id = currentDatasetId.value;
  }

  try {
    let res;
    if (isEditMode.value) {
      // 修改数据集
      res = await postUpdateDataSet(payload);
    } else {
      // 创建数据集
      res = await postCreateDataSet(payload);
    }

    if (res) {
      ElMessage.success(isEditMode.value ? '数据集修改成功！' : '数据集保存成功！');
      showSaveDialog.value = false;

      // 保存成功后返回数据集首页
      setTimeout(() => {
        router.push('/data/dataset');
      }, 1000); // 延迟1秒让用户看到成功消息
    } else {
      ElMessage.error(res.message || (isEditMode.value ? '数据集修改失败' : '数据集保存失败'));
    }
  } catch (err) {
    console.error(isEditMode.value ? '修改数据集失败:' : '保存数据集失败:', err);
    ElMessage.error(isEditMode.value ? '修改数据集失败' : '保存数据集失败');
  }
};

const dataPreviewRef = ref(null)

// 获取字段类型颜色（根据数字类型）
function getFieldTypeColor(type) {
  const colorMap = {
    1: '#67c23a',   // 指标 - 绿色
    2: '#409eff',   // 维度 - 蓝色
    3: '#e6a23c'    // 统计周期 - 黄色
  };
  return colorMap[type] || '#909399';
}

// 获取字段类型颜色（根据文本类型）
function getFieldTypeColorByText(typeText) {
  const colorMap = {
    '指标': '#67c23a',     // 绿色
    '维度': '#409eff',     // 蓝色
    '统计周期': '#e6a23c'  // 黄色
  };
  return colorMap[typeText] || '#909399';
}

// 获取字段类型文本
function getFieldTypeText(type) {
  const textMap = {
    1: '指标',
    2: '维度',
    3: '统计周期'
  };
  return textMap[type] || '未知';
}

// 搜索变化处理
function handleSearchChange() {
  // 搜索功能已通过computed实现，这里可以添加额外逻辑
}

// 显示维度枚举选项
async function showDimensionOptions(row) {
  currentDimensionField.value = row;
  dimensionDialogVisible.value = true;

  try {
    // 根据数据源类型决定 tableNameOrSheetId 参数
    // 如果是EXCEL文件类型（本地文件数据源），传递数据表ID
    // 如果是数据库类型数据源，传递表名
    const tableNameOrSheetId = selectedTable.value.id ? selectedTable.value.id : selectedTable.value.tableProp;

    // 构建请求参数
    const params = {
      columnName: row.name, // 当前行的字段名称
      dataSourceId: getDataSourceId.value,
      tableNameOrSheetId: tableNameOrSheetId
    };

    const res = await getDimensionOptions(params);
    console.log(res, "维度的枚举值列表：");

    if (res && Array.isArray(res)) {
      dimensionOptions.value = res;
    } else {
      dimensionOptions.value = [];
    }
  } catch (error) {
    console.error('获取维度枚举失败:', error);
    ElMessage.error('获取维度枚举失败');
    dimensionOptions.value = [];
  }
}

// 业务名称选择处理
function handleBusinessNameSelect(row) {
  stdNameSelected.value = row.businessName;
  stdNameDefinition.value = row.termDefinitions;
  selectedBusinessRowKey.value = row.id;
  // 同时更新字段类型
  stdNameEditRow.value.fieldType = row.type;
}

// 打开弹窗
async function openStdNameDialog(row) {
  stdNameEditRow.value = row;
  stdNameDialogVisible.value = true;
  stdNameSearch.value = '';
  stdNameSelected.value = row.stdName || '';
  stdNameDefinition.value = '';
  selectedBusinessRowKey.value = null;

  // 调用接口获取业务名称数据
  try {
    const params = {
      pageNo: 1,
      pageSize: -1,
      type: '1,2,3' // 固定参数：1指标 2维度 3统计周期
    };
    const res = await getBusinessName(params);
    if (res && res.list) {
      stdNameOptionsRaw.value = res.list;
      // 如果当前行已有业务名称，设置选中状态
      if (row.stdName) {
        const selectedItem = res.list.find(item => item.businessName === row.stdName);
        if (selectedItem) {
          selectedBusinessRowKey.value = selectedItem.id;
          stdNameDefinition.value = selectedItem.termDefinitions;
        }
      }
    }
  } catch (error) {
    console.error('获取业务名称失败:', error);
    ElMessage.error('获取业务名称失败');
  }
}



function saveStdName() {
  if (stdNameSelected.value) {
    stdNameEditRow.value.stdName = stdNameSelected.value;
    // 如果选择了业务名称，同时更新字段类型
    const selectedOption = stdNameOptionsRaw.value.find(item => item.businessName === stdNameSelected.value);
    if (selectedOption) {
      stdNameEditRow.value.isIndex = getFieldTypeText(selectedOption.type);
      stdNameEditRow.value.descriptionValue = selectedOption.termDefinitions;
    }
  }
  stdNameDialogVisible.value = false;
}

// 修改数据源选择下拉框的显示
const sourceDisplayName = computed(() => {
  if (selectedSource.value) {
    const typeMap = {
      1: '本地数据源',
      2: '数据库数据源',
      3: 'API数据源'
    };
    const typeName = typeMap[selectedSource.value.type] || '';
    return `${selectedSource.value.name} (${typeName})`;
  }
  return '';
});

</script>

<style lang="scss" scoped>
.dataset-main-layout {
  display: flex;
  height: 100vh;
  background: #fff;
}

.dataset-tree-panel {
  width: 320px;
  padding: 24px;
  background: #fff;
  border-right: 1px solid #eee;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;

  &.edit-mode {
    .source-select-input {
      background-color: #f5f7fa;
      cursor: not-allowed;
    }

    // .table-item {
    //   cursor: not-allowed;
    //   opacity: 0.7;

    //   &.active {
    //     opacity: 1;
    //     cursor: default;
    //   }
    // }
  }

  // 从数据源页面跳转时的样式
  &.from-data-source {
    .source-select-input {
      background-color: #f0f9ff;
      border-color: #409eff;
      color: #409eff;
      cursor: not-allowed;
    }

    // .table-item {
    //   cursor: not-allowed;
    //   opacity: 0.7;

    //   &.active {
    //     opacity: 1;
    //     cursor: default;
    //     background: #ecf5ff;
    //     border-color: #409eff;
    //   }
    // }
  }
}

.data-table-list {
  flex: 1;
  overflow-y: auto;
  margin-top: 8px;
}

.tree-header {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
}

.source-select {
  margin-bottom: 16px;

  .source-select-input {
    width: 100%;
  }

  .source-tip {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 8px;
    padding: 8px 12px;
    background: #f0f9ff;
    border: 1px solid #409eff;
    border-radius: 4px;
    color: #409eff;
    font-size: 12px;

    .el-icon {
      font-size: 14px;
    }
  }
}

.source-dialog-content {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.search-box {
  margin-bottom: 16px;
}

.source-list-container {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 8px;
}

.source-list-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  background: #fff;
  border: 1px solid #eee;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background: #f5f7fa;
    border-color: #409eff;
  }

  &.active {
    background: #ecf5ff;
    border-color: #409eff;
  }

  .source-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    color: #303133;
  }

  .source-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #909399;

    .source-type {
      background: #f0f2f5;
      padding: 2px 6px;
      border-radius: 2px;
    }

    .source-creator {
      color: #909399;
    }
  }
}

.table-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  background: #fff;
  border: 1px solid #eee;
  transition: all 0.3s;

  // 可点击状态
  &.clickable {
    cursor: pointer;

    &:hover {
      background: #f5f7fa;
      border-color: #409eff;
    }
  }

  // 禁用状态
  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;

    &:hover {
      background: #fff;
      border-color: #eee;
    }
  }

  &.active {
    background: #ecf5ff;
    border-color: #409eff;
    position: relative;
    opacity: 1 !important; // 确保选中状态不受禁用影响

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background-color: #409eff;
      border-radius: 4px 0 0 4px;
    }

    // 选中状态下如果可点击，保持pointer样式
    &.clickable {
      cursor: pointer;
    }

    // 选中状态下如果禁用，使用default样式
    &.disabled {
      cursor: default;
    }
  }

  .table-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    color: #303133;
  }

  .table-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #909399;

    .table-type {
      background: #f0f2f5;
      padding: 2px 6px;
      border-radius: 2px;
    }
  }
}

.dataset-content-panel {
  flex: 1;
  padding: 24px 32px 0 32px;
  display: flex;
  flex-direction: column;
}

.dataset-header-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 18px;

  .dataset-header-title {
    font-size: 22px;
    font-weight: bold;
    margin-right: 24px;

  }

  .save-btn {
    margin-left: 22px;
  }
}

.dataset-tabs {
  margin-bottom: 12px;
}

.dataset-table-panel {
  flex: 1;
  overflow: auto;

}

.dataset-table {
  margin-top: 0;
}

/* 字段类型标签样式 */
.field-type-tag {
  font-weight: 500;
  border: none;
}

.field-type-indicator {
  background-color: #67c23a !important;
  color: #fff !important;
}

.field-type-dimension {
  background-color: #409eff !important;
  color: #fff !important;
}

.field-type-period {
  background-color: #e6a23c !important;
  color: #fff !important;
}

/* 业务名称单元格样式 */
.business-name-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.business-name-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.edit-icon {
  margin-left: 8px;
  cursor: pointer;
  color: #409eff;
  font-size: 14px;
  flex-shrink: 0;

  &:hover {
    color: #66b1ff;
  }
}

/* 空字段类型样式 */
.empty-field-type {
  color: #c0c4cc;
  font-style: italic;
}

/* 字段描述样式 */
.field-description {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 字段类型单元格样式 */
.field-type-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* 维度箭头样式 */
.dimension-arrow {
  margin-left: 8px;
  cursor: pointer;
  color: #409eff;
  font-size: 14px;
  flex-shrink: 0;

  &:hover {
    color: #66b1ff;
  }
}

/* 维度枚举列表样式 */
.dimension-list-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
}

.dimension-item {
  display: inline-block;
  margin: 2px;
  padding: 4px;
  background-color: #e1f3d8;
  border: 1px solid #b3d8a4;
  border-radius: 16px;
  font-size: 12px;
  color: #67c23a;
  white-space: nowrap;

  &:hover {
    background-color: #d1e7c1;
  }
}
</style>