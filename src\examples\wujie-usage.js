/**
 * 无界微前端使用示例
 * 展示如何在主项目中集成这个数据管理模块
 */

// ===== 主项目中的集成代码示例 =====

/**
 * 1. 主项目中安装无界
 * npm install wujie-vue3
 */

/**
 * 2. 主项目中注册子应用
 */
const mainAppExample = `
// main.js
import { createApp } from 'vue'
import WujieVue from 'wujie-vue3'
import App from './App.vue'

const app = createApp(App)
app.use(WujieVue)

// 3. 在主项目组件中使用子应用
// DataManagementModule.vue
<template>
  <div class="data-management-wrapper">
    <WujieVue
      width="100%"
      height="100%"
      name="data-management"
      :url="dataManagementUrl"
      :props="childAppProps"
      :beforeLoad="beforeLoad"
      :beforeMount="beforeMount"
      :afterMount="afterMount"
      :beforeUnmount="beforeUnmount"
      @error="handleError"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 子应用URL
const dataManagementUrl = ref('http://localhost:3000')

// 传递给子应用的props
const childAppProps = computed(() => ({
  // 用户信息
  userInfo: {
    id: userStore.userInfo.id,
    name: userStore.userInfo.name,
    email: userStore.userInfo.email,
    avatar: userStore.userInfo.avatar,
    permissions: userStore.permissions,
    roles: userStore.roles
  },
  // 认证token
  token: userStore.token,
  // 租户信息
  tenantId: userStore.tenantId,
  // 配置信息
  config: {
    apiBaseUrl: 'http://237850r72u.zicp.vip/basic',
    features: {
      dataExport: true,
      dataImport: true,
      advancedFilter: true
    }
  }
}))

// 生命周期钩子
const beforeLoad = (appWindow) => {
  console.log('数据管理模块开始加载')
  // 可以在这里设置全局变量
  appWindow.__MAIN_APP_TOKEN__ = userStore.token
  appWindow.__MAIN_APP_USER_INFO__ = userStore.userInfo
}

const beforeMount = (appWindow) => {
  console.log('数据管理模块开始挂载')
}

const afterMount = (appWindow) => {
  console.log('数据管理模块挂载完成')
  
  // 监听子应用事件
  appWindow.$wujie?.bus?.$on('CHILD_APP_READY', () => {
    console.log('子应用已准备就绪')
  })
  
  appWindow.$wujie?.bus?.$on('ROUTE_CHANGE', (route) => {
    console.log('子应用路由变化:', route)
    // 可以在这里更新主应用的面包屑导航等
  })
  
  appWindow.$wujie?.bus?.$on('REQUEST_TOKEN_REFRESH', () => {
    console.log('子应用请求刷新token')
    // 刷新token并通知子应用
    refreshTokenAndNotify(appWindow)
  })
  
  appWindow.$wujie?.bus?.$on('CHILD_APP_ERROR', (error) => {
    console.error('子应用发生错误:', error)
    // 可以在这里处理错误上报等
  })
}

const beforeUnmount = () => {
  console.log('数据管理模块即将卸载')
}

const handleError = (error) => {
  console.error('数据管理模块加载失败:', error)
}

// 刷新token并通知子应用
const refreshTokenAndNotify = async (appWindow) => {
  try {
    await userStore.refreshToken()
    
    // 通知子应用token已更新
    appWindow.$wujie?.bus?.$emit('TOKEN_UPDATE', userStore.token)
    
    // 更新全局变量
    appWindow.__MAIN_APP_TOKEN__ = userStore.token
  } catch (error) {
    console.error('刷新token失败:', error)
  }
}

// 监听用户信息变化
watch(() => userStore.userInfo, (newUserInfo) => {
  // 通知子应用用户信息已更新
  const appWindow = window.document.querySelector('iframe[name="data-management"]')?.contentWindow
  if (appWindow) {
    appWindow.$wujie?.bus?.$emit('USER_INFO_UPDATE', newUserInfo)
    appWindow.__MAIN_APP_USER_INFO__ = newUserInfo
  }
}, { deep: true })

</script>

<style scoped>
.data-management-wrapper {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
`

/**
 * 4. 主项目的用户store示例
 */
const userStoreExample = `
// stores/user.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  const userInfo = ref({
    id: '123456',
    name: '张三',
    email: '<EMAIL>',
    avatar: '/avatars/zhangsan.jpg',
    permissions: ['data:read', 'data:write', 'data:delete'],
    roles: ['admin', 'data_manager']
  })
  
  const token = ref('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...')
  const tenantId = ref('tenant_001')
  
  const isLoggedIn = computed(() => !!token.value)
  
  const refreshToken = async () => {
    // 刷新token的逻辑
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      headers: {
        'Authorization': \`Bearer \${token.value}\`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      token.value = data.token
    } else {
      throw new Error('刷新token失败')
    }
  }
  
  return {
    userInfo,
    token,
    tenantId,
    isLoggedIn,
    refreshToken
  }
})
`

// ===== 子应用（当前项目）中的使用示例 =====

/**
 * 5. 在子应用组件中使用主项目数据
 */
export const useMainAppData = () => {
  const { 
    getUserInfo, 
    getToken, 
    getUserPermissions,
    hasPermission 
  } = require('@/utils/auth.js')
  
  const { 
    getMainAppConfig,
    isFeatureSupported,
    emitToMainApp 
  } = require('@/utils/wujie.js')
  
  // 获取用户信息
  const userInfo = getUserInfo()
  console.log('当前用户:', userInfo)
  
  // 获取token
  const token = getToken()
  console.log('当前token:', token ? '已获取' : '未获取')
  
  // 检查权限
  const canDelete = hasPermission('data:delete')
  console.log('是否有删除权限:', canDelete)
  
  // 获取配置
  const config = getMainAppConfig()
  console.log('主应用配置:', config)
  
  // 检查功能支持
  const canExport = isFeatureSupported('dataExport')
  console.log('是否支持数据导出:', canExport)
  
  // 向主应用发送消息
  const notifyDataChange = (data) => {
    emitToMainApp('DATA_CHANGE', {
      type: 'datasource_updated',
      data: data,
      timestamp: Date.now()
    })
  }
  
  return {
    userInfo,
    token,
    canDelete,
    config,
    canExport,
    notifyDataChange
  }
}

/**
 * 6. 在Vue组件中使用
 */
export const componentExample = `
<template>
  <div class="data-source-page">
    <div v-if="userInfo" class="user-info">
      欢迎，{{ userInfo.name }}
    </div>
    
    <el-button 
      v-if="canDelete" 
      type="danger" 
      @click="deleteDataSource"
    >
      删除数据源
    </el-button>
    
    <el-button 
      v-if="canExport" 
      type="primary" 
      @click="exportData"
    >
      导出数据
    </el-button>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useMainAppData } from '@/examples/wujie-usage.js'

const {
  userInfo,
  canDelete,
  canExport,
  notifyDataChange
} = useMainAppData()

const deleteDataSource = () => {
  // 删除逻辑
  console.log('删除数据源')
  
  // 通知主应用数据变化
  notifyDataChange({
    action: 'delete',
    resourceType: 'datasource',
    resourceId: 'ds_123'
  })
}

const exportData = () => {
  // 导出逻辑
  console.log('导出数据')
}

onMounted(() => {
  console.log('组件挂载完成，用户信息:', userInfo)
})
</script>
`

console.log('无界微前端集成示例代码已准备就绪')
console.log('主项目集成代码:', mainAppExample)
console.log('用户store示例:', userStoreExample)
console.log('组件使用示例:', componentExample)
