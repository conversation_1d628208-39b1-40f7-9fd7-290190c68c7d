// 环境配置
const config = {
  // 基础API地址 - 修改为正确的后端地址
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://237850r72u.zicp.vip/basic',

  // 实际后端地址（用于文档和调试）
  backendURL: import.meta.env.VITE_BACKEND_URL || 'http://237850r72u.zicp.vip/',

  // 请求超时时间
  timeout: 10000,

  // 是否使用mock数据
  useMock: import.meta.env.VITE_USE_MOCK === 'true',

  // 是否开启调试模式
  debug: import.meta.env.VITE_DEBUG === 'true',

  // 当前环境
  env: import.meta.env.MODE || 'development'
}

export default config
