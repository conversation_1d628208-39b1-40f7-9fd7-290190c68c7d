# CORS跨域问题解决方案

## 问题描述
前端访问后端API时出现CORS跨域错误：
```
Access to XMLHttpRequest at 'http://237850r72u.zicp.vip/api/datasource/list' from origin 'http://localhost:3001' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## 解决方案：Vite代理配置

### 1. 配置文件修改

#### vite.config.js
```javascript
export default defineConfig({
  // ... 其他配置
  server: {
    port: 3000,
    open: true,
    // 配置代理解决CORS问题
    proxy: {
      '/api': {
        target: 'http://237850r72u.zicp.vip',
        changeOrigin: true,
        secure: false,
        // 重写路径，移除/api前缀
        rewrite: (path) => path.replace(/^\/api/, ''),
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('发送请求到目标服务器:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('从目标服务器收到响应:', proxyRes.statusCode, req.url);
          });
        }
      }
    }
  }
})
```

#### .env.development
```bash
# 开发环境使用代理路径
VITE_API_BASE_URL = '/api'
# 实际后端地址
VITE_BACKEND_URL = 'http://237850r72u.zicp.vip/'
```

#### .env.production
```bash
# 生产环境使用完整URL
VITE_API_BASE_URL = 'http://237850r72u.zicp.vip/'
```

### 2. 代理工作原理

1. **前端请求**: `http://localhost:3001/api/datasource/list`
2. **Vite代理**: 拦截 `/api` 开头的请求
3. **路径重写**: 移除 `/api` 前缀
4. **转发请求**: `http://237850r72u.zicp.vip/datasource/list`

### 3. 当前状态

✅ **CORS问题已解决**: 不再出现跨域错误
✅ **代理配置正确**: 请求正确转发到后端
⚠️ **API路径待确认**: 后端返回404，需要确认正确的API路径

## 当前测试结果

从控制台日志可以看到：
```
发送请求到目标服务器: GET /datasource/list
从目标服务器收到响应: 404 /datasource/list
```

这说明：
1. 代理配置工作正常
2. 请求成功到达后端服务器
3. 后端API路径可能不是 `/datasource/list`

## 下一步操作

### 1. 确认后端API路径
需要确认后端实际的API路径格式，可能是：
- `/api/datasource/list` (需要保留api前缀)
- `/datasource/list` (当前配置)
- `/data-source/list` (使用连字符)
- 其他路径格式

### 2. 调整代理配置
如果后端需要保留 `/api` 前缀，修改vite.config.js：
```javascript
proxy: {
  '/api': {
    target: 'http://237850r72u.zicp.vip',
    changeOrigin: true,
    secure: false,
    // 不重写路径，保持/api前缀
    // rewrite: (path) => path.replace(/^\/api/, ''),
  }
}
```

### 3. 测试建议
1. 在浏览器中访问 `http://localhost:3001/`
2. 点击"测试接口"按钮
3. 查看控制台日志确认请求路径
4. 根据后端API文档调整路径

## 备选解决方案

### 方案1：后端配置CORS
如果可以修改后端，添加CORS头：
```javascript
// Express.js示例
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE');
  res.header('Access-Control-Allow-Headers', 'Content-Type');
  next();
});
```

### 方案2：浏览器插件
开发阶段可以使用CORS浏览器插件临时解决

### 方案3：nginx代理
生产环境可以使用nginx配置反向代理

## 总结

✅ CORS跨域问题已通过Vite代理解决
✅ 开发环境配置完成
✅ 生产环境配置准备就绪
⚠️ 需要确认后端API的正确路径格式

当前配置已经解决了CORS问题，只需要根据后端实际API路径进行微调即可。
