# API配置修改总结

## 修改概述

根据您的要求，已完成以下修改：

1. **修正基础URL**: 将API基础地址改为正确的 `http://237850r72u.zicp.vip/basic`
2. **移除多余前缀**: 删除所有API接口路径中多余的 `/api` 前缀
3. **添加Token认证**: 实现自动Token认证机制

## 具体修改内容

### 1. 配置文件修改

#### `src/config/index.js`
```javascript
// 修改前
baseURL: import.meta.env.VITE_API_BASE_URL || '/api'

// 修改后  
baseURL: import.meta.env.VITE_API_BASE_URL || 'http://237850r72u.zicp.vip/basic'
```

#### `src/config/request.js`
- 添加了 `import { getToken, clearAuth } from '@/utils/auth.js'`
- 启用了Token认证：自动在请求头添加 `Authorization: Bearer {token}`
- 完善了401错误处理：自动清除过期的认证信息

### 2. API接口修改

#### `src/api/datainfor/dataupload.js`
移除了所有接口路径中的 `/api` 前缀：

| 修改前 | 修改后 |
|--------|--------|
| `/datasource/list` | `/datasource/list` (无变化) |
| `/api/datasource/{id}/tables` | `/datasource/{id}/tables` |
| `/api/datasource/field/detail` | `/datasource/field/detail` |
| `/api/datasource/data/preview` | `/datasource/data/preview` |
| `/api/datasource/{id}` | `/datasource/{id}` |
| `/api/datasource/database/config/{typeId}` | `/datasource/database/config/{typeId}` |

### 3. 新增文件

#### `src/utils/auth.js`
新增认证工具函数，包含：
- `getToken()`: 获取当前token
- `setToken(token, remember)`: 设置token
- `removeToken()`: 移除token
- `getUserInfo()`: 获取用户信息
- `setUserInfo(userInfo, remember)`: 设置用户信息
- `clearAuth()`: 清除所有认证信息
- `isLoggedIn()`: 检查登录状态
- `isTokenExpired(token)`: 检查token是否过期

#### `src/examples/api-usage.js`
创建了完整的API使用示例，包含：
- 登录和设置token
- 各种API接口的调用示例
- 错误处理示例
- 完整工作流程示例

## 最终API地址

现在所有API请求的完整地址为：

| 接口 | 完整URL |
|------|---------|
| 获取数据源列表 | `http://237850r72u.zicp.vip/basic/datasource/list` |
| 获取数据源详情 | `http://237850r72u.zicp.vip/basic/datasource/{id}/tables` |
| 获取字段详情 | `http://237850r72u.zicp.vip/basic/datasource/field/detail` |
| 获取数据预览 | `http://237850r72u.zicp.vip/basic/datasource/data/preview` |
| 更新数据源 | `http://237850r72u.zicp.vip/basic/datasource/{id}` |
| 删除数据源 | `http://237850r72u.zicp.vip/basic/datasource/{id}` |
| 获取数据库配置 | `http://237850r72u.zicp.vip/basic/datasource/database/config/{typeId}` |

## 使用方法

### 1. 设置Token
```javascript
import { setToken } from '@/utils/auth.js'

// 登录成功后设置token
setToken('your-jwt-token-here', true) // true表示记住登录状态
```

### 2. 调用API
```javascript
import { getDataSourceList } from '@/api/datainfor/dataupload.js'

// 获取数据源列表（会自动添加token）
const data = await getDataSourceList()
```

### 3. 错误处理
- 401错误会自动清除认证信息并提示重新登录
- 其他错误会显示相应的错误信息

## 注意事项

1. **必须先设置Token**: 使用API前必须通过 `setToken()` 设置用户token
2. **自动认证**: 所有请求都会自动添加Bearer Token
3. **错误处理**: 401错误时会自动清除认证信息
4. **存储方式**: 支持localStorage（记住登录）和sessionStorage（临时登录）
5. **环境变量**: 可通过 `VITE_API_BASE_URL` 环境变量覆盖默认配置

## 测试建议

1. 先通过 `setToken()` 设置一个有效的token
2. 调用 `getDataSourceList()` 测试基本功能
3. 检查浏览器网络面板，确认请求地址正确
4. 验证请求头中包含正确的Authorization字段

## 文件清单

修改的文件：
- `src/config/index.js`
- `src/config/request.js` 
- `src/api/datainfor/dataupload.js`
- `API_CONFIG.md`

新增的文件：
- `src/utils/auth.js`
- `src/examples/api-usage.js`
- `MODIFICATION_SUMMARY.md`

所有修改已完成，API现在使用正确的地址 `http://237850r72u.zicp.vip/basic` 并支持自动Token认证。
